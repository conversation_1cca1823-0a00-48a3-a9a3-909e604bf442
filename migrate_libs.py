"""
迁移JavaScript库文件脚本
将media/js/中的必要库文件复制到新的前端结构中
"""

import os
import shutil
from pathlib import Path

def migrate_js_libs():
    """迁移JavaScript库文件"""
    
    # 源目录和目标目录
    source_dir = Path("media/js")
    target_dir = Path("frontend/static/js/libs")
    
    # 需要迁移的库文件
    lib_files = [
        "vue.js",
        "axios.min.js", 
        "dayjs.min.js",
        "marked.min.js",
        "FileSaver.min.js",
        "xlsx.full.min.js"
    ]
    
    # 创建目标目录
    target_dir.mkdir(parents=True, exist_ok=True)
    
    print("开始迁移JavaScript库文件...")
    
    migrated_count = 0
    for lib_file in lib_files:
        source_file = source_dir / lib_file
        target_file = target_dir / lib_file
        
        if source_file.exists():
            try:
                shutil.copy2(source_file, target_file)
                print(f"✓ 已迁移: {lib_file}")
                migrated_count += 1
            except Exception as e:
                print(f"✗ 迁移失败 {lib_file}: {e}")
        else:
            print(f"✗ 源文件不存在: {lib_file}")
    
    print(f"\n迁移完成！成功迁移 {migrated_count}/{len(lib_files)} 个库文件")
    
    return migrated_count == len(lib_files)

def update_base_template():
    """更新base.html模板以使用新的库文件路径"""
    
    base_template_path = Path("frontend/templates/base.html")
    
    if not base_template_path.exists():
        print("✗ base.html模板不存在")
        return False
    
    try:
        with open(base_template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新库文件路径
        replacements = {
            'src="/media/js/vue.js"': 'src="/frontend/static/js/libs/vue.js"',
            'src="/media/js/axios.min.js"': 'src="/frontend/static/js/libs/axios.min.js"',
            'src="/media/js/dayjs.min.js"': 'src="/frontend/static/js/libs/dayjs.min.js"',
            'src="/media/js/marked.min.js"': 'src="/frontend/static/js/libs/marked.min.js"',
            'src="/media/js/FileSaver.min.js"': 'src="/frontend/static/js/libs/FileSaver.min.js"',
            'src="/media/js/xlsx.full.min.js"': 'src="/frontend/static/js/libs/xlsx.full.min.js"'
        }
        
        updated = False
        for old_path, new_path in replacements.items():
            if old_path in content:
                content = content.replace(old_path, new_path)
                updated = True
        
        if updated:
            with open(base_template_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print("✓ 已更新base.html模板中的库文件路径")
        else:
            print("ℹ base.html模板无需更新")
        
        return True
        
    except Exception as e:
        print(f"✗ 更新base.html模板失败: {e}")
        return False

def clean_old_structure():
    """清理原有结构（谨慎操作）"""
    
    print("\n准备清理原有结构...")
    
    # 需要删除的文件和目录
    items_to_remove = [
        "media/index.html",  # 原有主页面
        "media/js/index.js",  # 原有主页面脚本
        "index.py",  # 原有入口文件
        "urls.py"   # 原有路由文件
    ]
    
    # 需要保留的文件（暂时不删除，以防需要回退）
    items_to_keep = [
        "media/js/",  # JavaScript库文件目录
        "media/index_images.html",  # 图片管理页面
        "media/audit_images.html",  # 图片审核页面
        "media/index_log.html",  # 日志管理页面
        "media/js/script_images.js",  # 图片管理脚本
        "media/js/script_audit_images.js",  # 图片审核脚本
        "media/js/script_log.js"  # 日志管理脚本
    ]
    
    print("将要删除的文件:")
    for item in items_to_remove:
        if os.path.exists(item):
            print(f"  - {item}")
        else:
            print(f"  - {item} (不存在)")
    
    print("\n将要保留的文件:")
    for item in items_to_keep:
        if os.path.exists(item):
            print(f"  + {item}")
    
    # 询问用户确认
    confirm = input("\n确定要删除这些文件吗？(y/N): ").lower().strip()
    
    if confirm == 'y':
        removed_count = 0
        for item in items_to_remove:
            if os.path.exists(item):
                try:
                    if os.path.isdir(item):
                        shutil.rmtree(item)
                    else:
                        os.remove(item)
                    print(f"✓ 已删除: {item}")
                    removed_count += 1
                except Exception as e:
                    print(f"✗ 删除失败 {item}: {e}")
            else:
                print(f"ℹ 文件不存在: {item}")
        
        print(f"\n清理完成！删除了 {removed_count} 个文件/目录")
        return True
    else:
        print("取消清理操作")
        return False

def main():
    """主函数"""
    print("=== 知识库系统迁移脚本 ===\n")
    
    # 步骤1: 迁移JavaScript库文件
    if migrate_js_libs():
        print("✓ JavaScript库文件迁移成功")
    else:
        print("✗ JavaScript库文件迁移失败")
        return
    
    # 步骤2: 更新模板文件
    if update_base_template():
        print("✓ 模板文件更新成功")
    else:
        print("✗ 模板文件更新失败")
        return
    
    # 步骤3: 清理原有结构（可选）
    print("\n" + "="*50)
    print("迁移完成！新的模块化结构已准备就绪。")
    print("\n可选操作：清理原有结构")
    print("注意：这将删除原有的文件，请确保新结构工作正常后再执行。")
    
    clean_confirm = input("是否现在清理原有结构？(y/N): ").lower().strip()
    if clean_confirm == 'y':
        clean_old_structure()
    else:
        print("保留原有结构，可以稍后手动清理。")
    
    print("\n=== 迁移完成 ===")
    print("新的知识库管理系统已准备就绪！")
    print("启动命令: python main.py")

if __name__ == "__main__":
    main()
