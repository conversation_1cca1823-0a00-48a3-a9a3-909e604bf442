# 知识库管理系统 - 重构版本

## 项目概述

本项目是知识库管理系统的重构版本，采用模块化架构设计，提高了代码的可维护性、可扩展性和可测试性。

## 重构目标

1. **模块化设计**: 将原有的单一大文件拆分为多个功能模块
2. **分层架构**: 实现业务逻辑、数据访问、API处理的分离
3. **向后兼容**: 确保现有功能不受影响
4. **代码质量**: 提高代码的可读性和可维护性

## 项目结构

```
zshpro/
├── core/                    # 核心业务模块
│   ├── knowledge_base/      # 知识库模块
│   │   ├── __init__.py
│   │   ├── models.py        # 数据模型
│   │   ├── services.py      # 业务逻辑
│   │   ├── handlers.py      # API处理器
│   │   └── schemas.py       # 数据验证模式
│   ├── file_management/     # 文件管理模块
│   │   ├── __init__.py
│   │   ├── services.py      # 文件处理业务逻辑
│   │   └── handlers.py      # 文件管理API处理器
│   ├── audit/              # 审核模块
│   │   ├── __init__.py
│   │   ├── services.py      # 审核业务逻辑
│   │   └── handlers.py      # 审核API处理器
│   └── auth/               # 认证模块
│       ├── __init__.py
│       ├── services.py      # 认证业务逻辑
│       └── handlers.py      # 认证API处理器
├── database/               # 数据访问层
│   ├── __init__.py
│   ├── connection.py       # 数据库连接管理
│   └── repositories/       # 数据访问仓库
│       ├── __init__.py
│       ├── dataset_repository.py
│       ├── collection_repository.py
│       └── data_repository.py
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── ai_client.py        # AI客户端封装
│   ├── file_utils.py       # 文件处理工具
│   └── validators.py       # 数据验证工具
├── config/                 # 配置模块
│   ├── __init__.py
│   ├── settings.py         # 应用设置
│   └── constants.py        # 常量定义
├── api/                    # API模块
│   ├── __init__.py
│   └── routes.py           # 模块化路由
├── media/                  # 静态资源（前端文件）
├── main.py                 # 新的主入口文件
├── index.py                # 原入口文件（兼容性）
├── urls.py                 # 原路由文件（兼容性）
├── test_refactored_system.py  # 系统测试脚本
└── README_REFACTORED.md    # 本文档
```

## 模块说明

### 核心业务模块 (core/)

- **knowledge_base/**: 知识库相关功能，包括数据集管理、集合管理等
- **file_management/**: 文件管理功能，包括文件上传、处理、删除等
- **audit/**: 审核功能，包括文档审核、数据更新等
- **auth/**: 认证功能，包括用户登录、权限验证等

### 数据访问层 (database/)

- **connection.py**: 统一的数据库连接管理
- **repositories/**: 数据访问仓库，封装数据库操作

### 工具模块 (utils/)

- **ai_client.py**: AI服务客户端封装
- **file_utils.py**: 文件处理相关工具函数
- **validators.py**: 数据验证工具

### 配置模块 (config/)

- **settings.py**: 应用程序配置
- **constants.py**: 系统常量定义

## 启动方式

### 使用新的模块化结构

```bash
python main.py
```

### 使用原有结构（向后兼容）

```bash
python index.py
```

## 测试

运行测试脚本验证系统功能：

```bash
python test_refactored_system.py
```

## API文档

重构后的API保持与原有系统完全兼容，同时增加了更好的文档和错误处理。

### 主要API端点

- **知识库管理**
  - `GET /getDatasList/` - 获取数据集列表
  - `GET /getCollectionListInfo/` - 获取集合列表
  - `GET /getDatasetdatas/` - 获取数据集数据

- **文件管理**
  - `POST /uploadfiles/` - 上传文件
  - `GET /deleteCollection/` - 删除集合
  - `GET /getFileInfo/` - 获取文件信息

- **审核管理**
  - `POST /auditCollection/` - 审核集合
  - `POST /updateDatasetdatas/` - 更新数据
  - `POST /deleteQA/` - 删除QA数据

- **认证**
  - `POST /login/` - 用户登录

## 重构优势

1. **可维护性**: 代码结构清晰，职责分离明确
2. **可扩展性**: 新功能易于添加，不影响现有模块
3. **可测试性**: 模块化设计便于单元测试
4. **性能优化**: 按需加载，减少资源浪费
5. **团队协作**: 不同开发者可以专注不同模块
6. **向后兼容**: 现有功能完全保持不变

## 注意事项

1. 重构过程中保持了完全的向后兼容性
2. 原有的API接口和功能保持不变
3. 数据库结构和数据完全不受影响
4. 前端页面无需任何修改

## 下一步计划

1. 前端页面模块化重构
2. 添加更多单元测试
3. 性能优化和监控
4. API文档完善
