/**
 * 数据管理模块
 * 处理数据的获取、更新、删除等操作
 */

const DataManager = {
  /**
   * 获取数据集列表
   */
  async getDatasetsList() {
    try {
      const response = await ApiClient.get('/getDatasList/');
      return response;
    } catch (error) {
      console.error('获取数据集列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取集合列表信息
   */
  async getCollectionListInfo(params = {}) {
    try {
      const response = await ApiClient.get('/getCollectionListInfo/', params);
      return response;
    } catch (error) {
      console.error('获取集合列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取数据集数据
   */
  async getDatasetDatas(collectionId) {
    try {
      const response = await ApiClient.get('/getDatasetdatas/', { collectionId });
      return response;
    } catch (error) {
      console.error('获取数据集数据失败:', error);
      throw error;
    }
  },

  /**
   * 更新数据集数据
   */
  async updateDatasetDatas(data) {
    try {
      const response = await ApiClient.post('/updateDatasetdatas/', { data });
      return response;
    } catch (error) {
      console.error('更新数据集数据失败:', error);
      throw error;
    }
  },

  /**
   * 删除QA数据
   */
  async deleteQA(id) {
    try {
      const response = await ApiClient.post('/deleteQA/', { id });
      return response;
    } catch (error) {
      console.error('删除QA数据失败:', error);
      throw error;
    }
  },

  /**
   * 删除集合
   */
  async deleteCollection(collectionId) {
    try {
      const response = await ApiClient.get('/deleteCollection/', { collectionId });
      return response;
    } catch (error) {
      console.error('删除集合失败:', error);
      throw error;
    }
  },

  /**
   * 获取相同型号文件
   */
  async getSameNameFiles(cpxh, excludeId) {
    try {
      const response = await ApiClient.get('/getSameNameFiles/', { cpxh, excludeId });
      return response;
    } catch (error) {
      console.error('获取相同型号文件失败:', error);
      throw error;
    }
  }
};

// 导出到全局
window.DataManager = DataManager;
