/* 知识库管理系统 - 专用样式 */

/* ==================== 文件网格布局 ==================== */
.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  padding: 20px;
}

/* ==================== 文件卡片增强样式 ==================== */
.file-card {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.file-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.file-card .el-card__body {
  padding: 20px;
}

/* ==================== 状态指示器 ==================== */
.status-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.status-badge.unreviewed {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
  border: 1px solid rgba(255, 77, 79, 0.2);
}

.status-badge.reviewed {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
  border: 1px solid rgba(82, 196, 26, 0.2);
}

/* ==================== 文件图标和信息 ==================== */
.file-icon {
  font-size: 48px;
  color: #409EFF;
  margin-bottom: 16px;
}

.file-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 2.8em;
}

.file-subtitle {
  font-size: 14px;
  color: #909399;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-meta-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #C0C4CC;
  margin-bottom: 16px;
}

/* ==================== 操作按钮 ==================== */
.file-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.file-actions .el-button {
  padding: 8px 16px;
  font-size: 12px;
}

/* ==================== 工具栏增强 ==================== */
.enhanced-toolbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.enhanced-toolbar .el-input__inner,
.enhanced-toolbar .el-select .el-input__inner {
  background: rgba(255, 255, 255, 0.9);
  border: none;
}

.enhanced-toolbar .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.enhanced-toolbar .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* ==================== 空状态样式 ==================== */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state-icon {
  font-size: 64px;
  color: #E4E7ED;
  margin-bottom: 16px;
}

.empty-state-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.empty-state-subtext {
  font-size: 14px;
  color: #C0C4CC;
}

/* ==================== 加载状态 ==================== */
.loading-state {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  font-size: 32px;
  color: #409EFF;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==================== 对话框增强 ==================== */
.enhanced-dialog .el-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.enhanced-dialog .el-dialog__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
}

.enhanced-dialog .el-dialog__title {
  color: white;
  font-weight: 500;
}

.enhanced-dialog .el-dialog__headerbtn .el-dialog__close {
  color: white;
}

/* ==================== 上传区域样式 ==================== */
.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  transition: all 0.3s;
}

.upload-area:hover {
  border-color: #409EFF;
  background: rgba(64, 158, 255, 0.05);
}

.upload-area.dragover {
  border-color: #409EFF;
  background: rgba(64, 158, 255, 0.1);
}

/* ==================== 进度条样式 ==================== */
.upload-progress {
  margin-top: 20px;
}

.upload-progress .el-progress-bar__outer {
  background: #f5f5f5;
  border-radius: 10px;
}

.upload-progress .el-progress-bar__inner {
  background: linear-gradient(90deg, #409EFF 0%, #36CFC9 100%);
  border-radius: 10px;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 10px;
    height: auto;
    padding: 15px;
  }
  
  .card-box {
    padding: 10px;
    height: calc(100vh - 200px);
  }
  
  .file-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 15px;
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .file-grid {
    grid-template-columns: 1fr;
    gap: 10px;
    padding: 10px;
  }
  
  .nav-menu {
    margin-top: 0;
    height: auto;
  }
  
  .main-container {
    flex-direction: column;
  }
}
