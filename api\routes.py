"""
API路由配置
重新组织的模块化路由结构
"""

from fastapi import APIRouter

# 导入各模块的处理器
from ..core.knowledge_base.handlers import (
    index, get_datasets_list, get_collection_list_info, 
    get_same_name_files, get_dataset_datas
)
from ..core.file_management.handlers import (
    get_file_info, upload_files, delete_collection, save_biaoshu
)
from ..core.audit.handlers import (
    audit_collection, update_dataset_datas, delete_qa
)
from ..core.auth.handlers import login

# 导入其他模块（保持兼容性）
try:
    from ..images_h5 import (
        h5_images, h5_images_audit_wh, getcollectionsList,
        getDatasetsDatas_images, uploadimage, whauditimage
    )
except ImportError:
    # 如果导入失败，创建占位符函数
    async def h5_images(): return {"error": "Module not available"}
    async def h5_images_audit_wh(): return {"error": "Module not available"}
    async def getcollectionsList(): return {"error": "Module not available"}
    async def getDatasetsDatas_images(): return {"error": "Module not available"}
    async def uploadimage(): return {"error": "Module not available"}
    async def whauditimage(): return {"error": "Module not available"}

try:
    from ..log_h5 import (
        applist, h5_log, getChatLogs, saveFeedbacks, getPaginationRecords
    )
except ImportError:
    # 如果导入失败，创建占位符函数
    async def applist(): return {"error": "Module not available"}
    async def h5_log(): return {"error": "Module not available"}
    async def getChatLogs(): return {"error": "Module not available"}
    async def saveFeedbacks(): return {"error": "Module not available"}
    async def getPaginationRecords(): return {"error": "Module not available"}

# 创建主路由器
router = APIRouter()

# ==================== 知识库相关路由 ====================
router.add_api_route("/", index, methods=["GET"], tags=["知识库"])
router.add_api_route("/index/", index, methods=["GET"], tags=["知识库"])
router.add_api_route("/getDatasList/", get_datasets_list, methods=["GET"], tags=["知识库"])
router.add_api_route("/getCollectionListInfo/", get_collection_list_info, methods=["GET"], tags=["知识库"])
router.add_api_route("/getSameNameFiles/", get_same_name_files, methods=["GET"], tags=["知识库"])
router.add_api_route("/getDatasetdatas/", get_dataset_datas, methods=["GET"], tags=["知识库"])

# ==================== 文件管理相关路由 ====================
router.add_api_route("/getFileInfo/", get_file_info, methods=["GET"], tags=["文件管理"])
router.add_api_route("/uploadfiles/", upload_files, methods=["POST"], tags=["文件管理"])
router.add_api_route("/deleteCollection/", delete_collection, methods=["GET"], tags=["文件管理"])
router.add_api_route("/savebs/", save_biaoshu, methods=["POST"], tags=["文件管理"])

# ==================== 审核相关路由 ====================
router.add_api_route("/auditCollection/", audit_collection, methods=["POST"], tags=["审核"])
router.add_api_route("/updateDatasetdatas/", update_dataset_datas, methods=["POST"], tags=["审核"])
router.add_api_route("/deleteQA/", delete_qa, methods=["POST"], tags=["审核"])

# ==================== 认证相关路由 ====================
router.add_api_route("/login/", login, methods=["POST"], tags=["认证"])

# ==================== 图片管理相关路由（保持兼容性）====================
router.add_api_route("/h5_images/", h5_images, methods=["GET"], tags=["图片管理"])
router.add_api_route("/h5_images_audit_wh/", h5_images_audit_wh, methods=["GET"], tags=["图片管理"])
router.add_api_route("/getcollectionsList/", getcollectionsList, methods=["GET"], tags=["图片管理"])
router.add_api_route("/getDatasetsDatas_images/", getDatasetsDatas_images, methods=["GET"], tags=["图片管理"])
router.add_api_route("/uploadimage/", uploadimage, methods=["POST"], tags=["图片管理"])
router.add_api_route("/whauditimage/", whauditimage, methods=["POST"], tags=["图片管理"])

# ==================== 日志管理相关路由（保持兼容性）====================
router.add_api_route("/applist/", applist, methods=["GET"], tags=["日志管理"])
router.add_api_route("/h5_log/", h5_log, methods=["GET"], tags=["日志管理"])
router.add_api_route("/getChatLogs/", getChatLogs, methods=["GET"], tags=["日志管理"])
router.add_api_route("/saveFeedbacks/", saveFeedbacks, methods=["POST"], tags=["日志管理"])
router.add_api_route("/getPaginationRecords/", getPaginationRecords, methods=["POST"], tags=["日志管理"])
