/* 内容审核 - 专用样式 */

/* ==================== 批量操作样式 ==================== */
.batch-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
}

.selected-count {
  color: #606266;
  font-size: 14px;
}

/* ==================== 审核数据列表样式 ==================== */
.audit-data-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.audit-data-item {
  margin-bottom: 20px;
}

.audit-card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  border-left: 4px solid #E6A23C;
}

.audit-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* ==================== 审核卡片头部 ==================== */
.audit-header {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 15px;
}

.data-id {
  font-size: 12px;
  color: #909399;
  font-family: monospace;
  flex: 1;
}

.audit-actions {
  display: flex;
  gap: 8px;
}

/* ==================== 审核内容样式 ==================== */
.audit-content h4 {
  margin: 15px 0 8px 0;
  font-size: 14px;
  color: #303133;
  font-weight: 600;
}

.content-display {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #E6A23C;
  line-height: 1.6;
  color: #606266;
  word-break: break-word;
}

.question-section .content-display {
  border-left-color: #E6A23C;
}

.answer-section .content-display {
  border-left-color: #67C23A;
}

/* ==================== 图片网格样式 ==================== */
.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.image-item {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  background: #fff;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
}

.image-item:hover {
  border-color: #E6A23C;
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.2);
}

.thumbnail {
  width: 100%;
  height: 80px;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s;
}

.thumbnail:hover {
  transform: scale(1.05);
}

/* ==================== 元数据样式 ==================== */
.metadata-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.metadata-item {
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #909399;
  font-size: 13px;
}

.metadata-key {
  font-weight: 600;
  color: #303133;
  margin-right: 8px;
}

.metadata-value {
  color: #606266;
}

/* ==================== 审核状态样式 ==================== */
.audit-card.approved {
  border-left-color: #67C23A;
}

.audit-card.rejected {
  border-left-color: #F56C6C;
}

.audit-card.pending {
  border-left-color: #E6A23C;
}

/* ==================== 图片预览样式 ==================== */
.image-preview {
  text-align: center;
  max-height: 70vh;
  overflow: auto;
}

.image-preview img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .audit-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .audit-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .batch-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .images-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
  }
  
  .thumbnail {
    height: 60px;
  }
  
  .metadata-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .audit-actions {
    flex-direction: column;
    gap: 5px;
  }
  
  .audit-actions .el-button {
    width: 100%;
  }
  
  .images-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 6px;
  }
  
  .thumbnail {
    height: 50px;
  }
}
