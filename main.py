"""
新的主入口文件
使用重构后的模块化结构
"""

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from apscheduler.schedulers.background import BackgroundScheduler

# 导入新的模块化路由
from api.routes import router
from core.knowledge_base.services import knowledge_base_service

# 导入新的定时任务函数
from utils.scheduler import sync_indexs

def create_app() -> FastAPI:
    """创建FastAPI应用"""
    app = FastAPI(
        title="知识库管理系统",
        description="重构后的知识库管理系统API",
        version="2.0.0"
    )
    
    # 挂载新的前端静态文件目录（优先）
    try:
        app.mount("/frontend", StaticFiles(directory="frontend"), name="frontend")
        print("✓ 新前端目录挂载成功")
    except Exception as e:
        print(f"✗ 前端目录挂载失败: {e}")
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 包含路由
    app.include_router(router)
    
    # 启动时初始化全局数据
    @app.on_event("startup")
    async def startup_event():
        """应用启动时的初始化"""
        knowledge_base_service.get_global_data()
        print("知识库系统启动完成，全局数据已加载")
    
    return app

# 创建应用实例
app = create_app()

# 启动定时任务（可选）
def start_scheduler():
    """启动定时任务"""
    scheduler = BackgroundScheduler()
    scheduler.add_job(sync_indexs, 'interval', seconds=60, max_instances=1)
    scheduler.start()
    print("定时任务调度器已启动")

if __name__ == "__main__":
    import uvicorn
    
    # 可选择是否启动定时任务
    # start_scheduler()
    
    uvicorn.run(app, host="0.0.0.0", port=8009)
