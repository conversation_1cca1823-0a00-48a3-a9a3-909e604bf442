{% extends "base.html" %}

{% block title %}图片管理{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/frontend/static/css/image-management.css">
{% endblock %}

{% block content %}
<el-container class="main-container">
  <!-- 侧边栏导航 -->
  {% include 'components/layout/sidebar.html' %}

  <!-- 右侧内容 -->
  <el-main style="padding:0">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <el-select v-model="selectedDataset" placeholder="选择数据集" style="width: 200px; margin-right: 10px;" @change="loadCollections">
        <el-option
          v-for="dataset in datasets"
          :key="dataset.id"
          :label="dataset.name"
          :value="dataset.id">
        </el-option>
      </el-select>

      <el-select v-model="selectedCollection" placeholder="选择集合" style="width: 200px; margin-right: 10px;" @change="loadImageData">
        <el-option
          v-for="collection in collections"
          :key="collection.id"
          :label="collection.name"
          :value="collection.id">
        </el-option>
      </el-select>

      <el-button type="primary" @click="loadImageData" :loading="loading">查询</el-button>
      <el-button type="success" @click="showUploadDialog">上传图片</el-button>
    </div>
    
    <!-- 图片数据展示 -->
    <div class="content-area">
      <div v-if="imageDataList.length === 0 && !loading" class="empty-state">
        <i class="el-icon-picture-outline"></i>
        <p>暂无图片数据</p>
      </div>
      
      <div v-else class="image-data-list">
        <div v-for="(item, index) in imageDataList" :key="index" class="image-data-item">
          <el-card class="data-card">
            <div class="data-header">
              <span class="data-id">ID: {{ item.id }}</span>
              <el-button type="text" @click="editData(item)" icon="el-icon-edit">编辑</el-button>
            </div>
            
            <div class="data-content">
              <div class="question-section">
                <h4>问题内容：</h4>
                <div class="content-display" v-html="item.processedQ"></div>
              </div>
              
              <div class="answer-section" v-if="item.a">
                <h4>答案内容：</h4>
                <div class="content-display">{{ item.a }}</div>
              </div>
              
              <div class="images-section" v-if="item.images && item.images.length > 0">
                <h4>相关图片：</h4>
                <div class="images-grid">
                  <div v-for="(image, imgIndex) in item.images" :key="imgIndex" class="image-item">
                    <img :src="image.src" @click="previewImage(image)" class="thumbnail">
                    <div class="image-actions">
                      <el-button type="text" @click="replaceImage(item, imgIndex)" icon="el-icon-refresh">替换</el-button>
                      <el-button type="text" @click="deleteImage(item, imgIndex)" icon="el-icon-delete" style="color: #F56C6C;">删除</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </el-main>
</el-container>

<!-- 编辑数据对话框 -->
<el-dialog
  title="编辑数据"
  :visible.sync="editDialogVisible"
  width="80%"
  top="5vh">
  
  <el-form :model="editForm" label-width="100px">
    <el-form-item label="问题内容">
      <el-input
        type="textarea"
        v-model="editForm.q"
        :rows="6"
        placeholder="请输入问题内容">
      </el-input>
    </el-form-item>
    
    <el-form-item label="答案内容">
      <el-input
        type="textarea"
        v-model="editForm.a"
        :rows="4"
        placeholder="请输入答案内容">
      </el-input>
    </el-form-item>
  </el-form>
  
  <div slot="footer" class="dialog-footer">
    <el-button @click="editDialogVisible = false">取消</el-button>
    <el-button type="primary" @click="saveEdit" :loading="saving">保存</el-button>
  </div>
</el-dialog>

<!-- 图片预览对话框 -->
<el-dialog
  title="图片预览"
  :visible.sync="previewDialogVisible"
  width="60%">
  
  <div class="image-preview">
    <img :src="previewImageSrc" style="width: 100%; height: auto;">
  </div>
  
  <div slot="footer" class="dialog-footer">
    <el-button @click="previewDialogVisible = false">关闭</el-button>
  </div>
</el-dialog>

<!-- 上传图片对话框 -->
<el-dialog
  title="上传图片"
  :visible.sync="uploadDialogVisible"
  width="500px">
  
  <el-upload
    ref="upload"
    :auto-upload="false"
    :on-change="handleImageChange"
    :on-remove="handleImageRemove"
    :file-list="uploadFileList"
    accept="image/*"
    multiple
    drag>
    <i class="el-icon-upload"></i>
    <div class="el-upload__text">将图片拖到此处，或<em>点击上传</em></div>
    <div class="el-upload__tip" slot="tip">只能上传jpg/png文件</div>
  </el-upload>
  
  <div slot="footer" class="dialog-footer">
    <el-button @click="uploadDialogVisible = false">取消</el-button>
    <el-button type="primary" @click="uploadImages" :loading="uploading">上传</el-button>
  </div>
</el-dialog>

<!-- 替换图片对话框 -->
<el-dialog
  title="替换图片"
  :visible.sync="replaceDialogVisible"
  width="500px">
  
  <el-upload
    ref="replaceUpload"
    :auto-upload="false"
    :on-change="handleReplaceImageChange"
    :file-list="replaceFileList"
    accept="image/*"
    drag>
    <i class="el-icon-upload"></i>
    <div class="el-upload__text">选择新图片替换</div>
    <div class="el-upload__tip" slot="tip">只能上传jpg/png文件</div>
  </el-upload>
  
  <div slot="footer" class="dialog-footer">
    <el-button @click="replaceDialogVisible = false">取消</el-button>
    <el-button type="primary" @click="confirmReplace" :loading="replacing">确认替换</el-button>
  </div>
</el-dialog>
{% endblock %}

{% block extra_js %}
<script src="/frontend/static/js/image-management.js"></script>
{% endblock %}
