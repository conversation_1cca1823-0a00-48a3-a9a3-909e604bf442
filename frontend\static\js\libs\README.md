# JavaScript 库文件

这个目录包含项目所需的第三方JavaScript库文件。

## 库文件说明

- `vue.js` - Vue.js 框架
- `axios.min.js` - HTTP 请求库
- `dayjs.min.js` - 日期处理库
- `marked.min.js` - Markdown 解析库
- `FileSaver.min.js` - 文件保存库
- `xlsx.full.min.js` - Excel 处理库

## 使用方式

这些库文件在 `base.html` 模板中被引用：

```html
<script src="/frontend/static/js/libs/vue.js"></script>
<script src="/frontend/static/js/libs/axios.min.js"></script>
<script src="/frontend/static/js/libs/dayjs.min.js"></script>
```

## 版本信息

- Vue.js: 2.x
- Axios: 最新版本
- Day.js: 最新版本
- Marked: 最新版本
- FileSaver: 最新版本
- XLSX: 最新版本
