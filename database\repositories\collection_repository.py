"""
集合仓库
处理集合相关的数据访问
"""

from typing import List, Dict, Any, Optional
from bson.objectid import ObjectId
from pymongo.database import Database
from datetime import datetime, timezone
from ..connection import get_database


class CollectionRepository:
    """集合数据访问仓库"""
    
    def __init__(self, db: Database = None):
        self.db = db or get_database()
        self.collection = self.db['dataset_collections']
    
    def get_collection_by_id(self, collection_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取单个集合"""
        return self.collection.find_one({"_id": ObjectId(collection_id)})
    
    def get_collections_by_dataset_ids(self, dataset_ids: List[ObjectId]) -> List[Dict[str, Any]]:
        """根据数据集ID列表获取集合"""
        return list(self.collection.find({"datasetId": {"$in": dataset_ids}}))
    
    def get_collections_dict(self, dataset_ids: List[ObjectId]) -> Dict[str, str]:
        """获取集合ID到名称的映射字典"""
        collections = self.get_collections_by_dataset_ids(dataset_ids)
        return {str(d["_id"]): d["name"] for d in collections}
    
    def find_collections_with_filters(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """根据过滤条件查找集合"""
        return list(self.collection.find(filters))
    
    def update_collection_metadata(self, collection_id: str, metadata: Dict[str, Any]):
        """更新集合元数据"""
        self.collection.update_one(
            {"_id": ObjectId(collection_id)},
            {"$set": {"metadata": metadata}}
        )
    
    def update_collection_audit_status(self, collection_id: str, audit_status: str, forbid: bool = False):
        """更新集合审核状态"""
        self.collection.update_one(
            {"_id": ObjectId(collection_id)},
            {"$set": {"metadata.audit": audit_status, "forbid": forbid}}
        )
    
    def get_same_model_files(self, model: str, exclude_id: str, dataset_ids: List[ObjectId]) -> List[Dict[str, Any]]:
        """获取相同型号的文件"""
        query = {
            "_id": {"$ne": ObjectId(exclude_id)},
            "datasetId": {"$in": dataset_ids},
            "type": "file",
            "metadata.cpxh": {"$regex": model, "$options": "i"}
        }
        return list(self.collection.find(query).sort("createTime", -1))


# 创建全局仓库实例
collection_repository = CollectionRepository()
