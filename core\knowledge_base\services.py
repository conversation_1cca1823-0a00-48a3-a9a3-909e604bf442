"""
知识库业务服务
处理知识库相关的业务逻辑
"""

import json
import requests
from typing import List, Dict, Any, Optional, Tuple
from bson.objectid import ObjectId
from datetime import datetime, timezone

from ...database.connection import get_database
from ...config.settings import settings
from ...config.constants import KNOWLEDGE_BASE_PROMPTS
from ...utils.ai_client import ai_client
from ...utils.file_utils import get_image_html, remove_image_html
from .models import DatasetModel, CollectionModel, DataModel


class KnowledgeBaseService:
    """知识库业务服务类"""
    
    def __init__(self):
        self.db = get_database()
        self._global_datasets_dic = {}
        self._global_collections_dic = {}
    
    def get_global_data(self):
        """获取全局数据集和集合信息"""
        self._global_datasets_dic = {}
        self._global_collections_dic = {}
        
        # 获取数据集列表
        dataids_list = [a["_id"] for a in self.db['datasets'].find({"parentId": ObjectId(settings.PARENT_DATASET_ID)})]
        dbdata = self.db['datasets'].find({"_id": {"$in": dataids_list}})
        self._global_datasets_dic = {str(d["_id"]): d["name"] for d in dbdata}
        
        # 获取集合列表
        collections = self.db['dataset_collections'].find({"datasetId": {"$in": dataids_list}})
        self._global_collections_dic = {str(d["_id"]): d["name"] for d in collections}
    
    @property
    def global_datasets_dic(self) -> Dict[str, str]:
        """获取全局数据集字典"""
        if not self._global_datasets_dic:
            self.get_global_data()
        return self._global_datasets_dic
    
    @property
    def global_collections_dic(self) -> Dict[str, str]:
        """获取全局集合字典"""
        if not self._global_collections_dic:
            self.get_global_data()
        return self._global_collections_dic
    
    def get_datasets_list(self) -> List[Dict[str, str]]:
        """获取数据集列表"""
        datasets_dic = [{"id": k, "name": v} for k, v in self.global_datasets_dic.items()]
        return datasets_dic
    
    def get_collection_list_info(self, keyword: str = "", start_date: str = "", 
                                end_date: str = "", status: str = "", dataset: str = "") -> List[Dict[str, Any]]:
        """获取集合列表信息"""
        query = {"metadata.source": "api"}
        
        if dataset:
            query["datasetId"] = ObjectId(dataset)
        if keyword:
            query["name"] = {"$regex": f".*{keyword}.*"}
        if start_date and end_date:
            query["$and"] = [
                {"createTime": {"$gte": datetime.fromisoformat(start_date).replace(tzinfo=timezone.utc)}},
                {"createTime": {"$lte": datetime.fromisoformat(end_date).replace(hour=23, minute=59, second=59, microsecond=999999, tzinfo=timezone.utc)}}
            ]
        if status:
            query["metadata.audit"] = status
        
        collections = self.db['dataset_collections'].find(query)
        result = []
        
        for collection in collections:
            result.append({
                "id": str(collection["_id"]),
                "datasetId": str(collection["datasetId"]),
                "datasetname": self.global_datasets_dic.get(str(collection["datasetId"]), ""),
                "name": collection["name"],
                "time": collection["createTime"],
                "audit": collection["metadata"]["audit"],
                "cpxh": collection["metadata"].get("cpxh", ""),
                "size": collection["metadata"]["size"]
            })
        
        return result
    
    def get_same_name_files(self, cpxh: str, exclude_id: str) -> List[Dict[str, Any]]:
        """获取相同型号的文件"""
        dataids_list = [a["_id"] for a in self.db['datasets'].find({"parentId": ObjectId(settings.PARENT_DATASET_ID)})]
        query = {
            "_id": {"$ne": ObjectId(exclude_id)},
            "datasetId": {"$in": dataids_list},
            "type": "file",
            "metadata.cpxh": {"$regex": cpxh, "$options": "i"}
        }
        
        collections = self.db['dataset_collections'].find(query).sort("createTime", -1)
        result = []
        
        for collection in collections:
            result.append({
                "id": str(collection["_id"]),
                "datasetId": str(collection["datasetId"]),
                "name": collection["name"],
                "time": collection["createTime"],
                "audit": collection["metadata"].get("audit", "1")
            })
        
        return result
    
    def get_dataset_datas(self, collection_id: str) -> Dict[str, Any]:
        """获取数据集数据"""
        try:
            audit = self.db['dataset_collections'].find_one({"_id": ObjectId(collection_id)})["metadata"]["audit"]
        except:
            audit = "1"
        
        dataids_list = list(self.db['dataset_datas'].find({"collectionId": ObjectId(collection_id)}).sort({"chunkIndex": 1}))
        result = []
        
        for data in dataids_list:
            indexes = self._get_indexes(data["indexes"])
            data["q"] = get_image_html(data["q"])
            result.append([str(data["_id"]), data["q"], data["a"] or '', indexes])
        
        return {
            "name": self.global_collections_dic.get(collection_id, ""),
            "q": result,
            "audit": audit
        }
    
    def _get_indexes(self, indexes: List[Dict[str, Any]], tag: int = 0) -> List[Dict[str, Any]]:
        """处理索引数据"""
        result = []
        for index in indexes:
            if index.get("defaultIndex") == True or index.get("type") == "default":
                index["_id"] = str(index["_id"])
                result.append(index)
            else:
                if tag == 1:
                    text = {"text": index["text"]}
                else:
                    text = {"text": index["text"], "editing": False}
                result.append(text)
        return result


# 创建全局服务实例
knowledge_base_service = KnowledgeBaseService()
