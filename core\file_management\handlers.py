"""
文件管理API处理器
处理文件管理相关的HTTP请求
"""

import json
from typing import Dict, Any
from fastapi import HTTPException, File, Form, UploadFile

from .services import file_management_service
from ..knowledge_base.services import knowledge_base_service
from ...core.knowledge_base.schemas import ApiResponse


async def get_file_info(filename: str) -> ApiResponse:
    """获取文件信息"""
    try:
        file_info = file_management_service.get_file_info(filename)
        return ApiResponse(code=200, data=file_info)
    except Exception as e:
        # 如果AI提取失败，返回默认值
        default_info = {
            "型号": "未知",
            "名称": "未知",
            "售前售后": "未知",
            "可接入软件": "未知"
        }
        return ApiResponse(code=200, data=default_info)


async def upload_files(
    file: UploadFile = File(...),
    type: str = Form(...),
    data: str = Form(...)
) -> ApiResponse:
    """上传文件"""
    try:
        # 验证知识库ID
        if type not in knowledge_base_service.global_datasets_dic:
            knowledge_base_service.get_global_data()
        
        if type not in knowledge_base_service.global_datasets_dic:
            raise HTTPException(status_code=400, detail="无效的知识库ID")
        
        # 读取文件内容
        file_content = await file.read()
        process_params = json.loads(data)
        
        # 上传文件
        result = file_management_service.upload_file(
            file_content=file_content,
            content_type=file.content_type,
            filename=file.filename,
            dataset_id=type,
            process_params=process_params
        )
        
        if result["code"] == 200:
            # 更新全局集合字典
            knowledge_base_service.get_global_data()
            return ApiResponse(code=200)
        else:
            raise HTTPException(status_code=result["code"], detail=result.get("message", "上传失败"))
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def delete_collection(collection_id: str) -> ApiResponse:
    """删除集合"""
    try:
        result = file_management_service.delete_collection(collection_id)
        
        if result["code"] == 200:
            return ApiResponse(code=200)
        else:
            raise HTTPException(status_code=result["code"], detail=result.get("message", "删除失败"))
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def save_biaoshu(body: dict) -> ApiResponse:
    """保存标书文件"""
    try:
        url_list = body.get("urllist", [])
        if not url_list:
            raise HTTPException(status_code=400, detail="URL列表不能为空")
        
        result = file_management_service.save_biaoshu_files(url_list)
        
        if result["code"] == 200:
            return ApiResponse(code=200, data=result["data"])
        else:
            raise HTTPException(status_code=result["code"], detail=result.get("message", "保存失败"))
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
