/* 图片管理 - 专用样式 */

/* ==================== 内容区域样式 ==================== */
.content-area {
  padding: 20px;
  min-height: calc(100vh - 140px);
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 64px;
  color: #E4E7ED;
  margin-bottom: 16px;
  display: block;
}

/* ==================== 图片数据列表样式 ==================== */
.image-data-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.image-data-item {
  margin-bottom: 20px;
}

.data-card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
}

.data-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* ==================== 数据卡片头部 ==================== */
.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 15px;
}

.data-id {
  font-size: 12px;
  color: #909399;
  font-family: monospace;
}

/* ==================== 数据内容样式 ==================== */
.data-content h4 {
  margin: 15px 0 8px 0;
  font-size: 14px;
  color: #303133;
  font-weight: 600;
}

.content-display {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #409EFF;
  line-height: 1.6;
  color: #606266;
  word-break: break-word;
}

.question-section .content-display {
  border-left-color: #409EFF;
}

.answer-section .content-display {
  border-left-color: #67C23A;
}

/* ==================== 图片网格样式 ==================== */
.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
  margin-top: 10px;
}

.image-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
}

.image-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.thumbnail {
  width: 100%;
  height: 100px;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s;
}

.thumbnail:hover {
  transform: scale(1.05);
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 8px;
  display: flex;
  justify-content: center;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-item:hover .image-actions {
  opacity: 1;
}

.image-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
  color: white;
  border: none;
  background: rgba(255, 255, 255, 0.2);
}

.image-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* ==================== 图片预览样式 ==================== */
.image-preview {
  text-align: center;
  max-height: 70vh;
  overflow: auto;
}

.image-preview img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* ==================== 上传区域样式 ==================== */
.el-upload-dragger {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  transition: all 0.3s;
}

.el-upload-dragger:hover {
  border-color: #409EFF;
  background: rgba(64, 158, 255, 0.05);
}

.el-upload-dragger.is-dragover {
  border-color: #409EFF;
  background: rgba(64, 158, 255, 0.1);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .image-data-list {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .images-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 10px;
  }
  
  .thumbnail {
    height: 80px;
  }
  
  .content-area {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .data-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .images-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 8px;
  }
  
  .thumbnail {
    height: 60px;
  }
}
