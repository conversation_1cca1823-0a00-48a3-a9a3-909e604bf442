"""
审核API处理器
处理审核相关的HTTP请求
"""

from typing import Dict, Any
from fastapi import HTTPException

from .services import audit_service
from ..knowledge_base.schemas import ApiResponse, AuditRequest, DataUpdateRequest


async def audit_collection(body: dict) -> ApiResponse:
    """审核集合"""
    try:
        collection_id = body.get("collectionId", "")
        token = body.get("token", "")
        
        result = audit_service.audit_collection(collection_id, token)
        
        if result["code"] == 200:
            return ApiResponse(code=200, data=result["data"])
        else:
            return ApiResponse(code=result["code"], message=result["message"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def update_dataset_datas(body: dict) -> ApiResponse:
    """更新数据集数据"""
    try:
        data_list = body.get("data", [])
        
        result = audit_service.update_dataset_data(data_list)
        
        if result["code"] == 200:
            return ApiResponse(code=200)
        else:
            return ApiResponse(code=result["code"], message=result["message"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def delete_qa(body: dict) -> ApiResponse:
    """删除QA数据"""
    try:
        data_id = body.get("id", "")
        
        result = audit_service.delete_qa_data(data_id)
        
        if result["code"] == 200:
            return ApiResponse(code=200)
        else:
            return ApiResponse(code=result["code"], message=result["message"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
