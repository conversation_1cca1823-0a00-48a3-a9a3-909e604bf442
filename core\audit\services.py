"""
审核业务服务
处理文档审核相关的业务逻辑
"""

import json
import requests
from typing import Dict, Any, List
from bson.objectid import ObjectId
from datetime import datetime

from ...database.connection import get_database
from ...config.settings import settings
from ...utils.file_utils import get_token, remove_image_html
from ..knowledge_base.services import knowledge_base_service


class AuditService:
    """审核业务服务类"""
    
    def __init__(self):
        self.db = get_database()
    
    def audit_collection(self, collection_id: str, token: str) -> Dict[str, Any]:
        """审核集合"""
        try:
            # 验证token
            password_md5 = get_token(settings.BASE_USERNAME, settings.BASE_PASSWORD)
            if token != password_md5:
                return {"code": 500, "message": "无权限审批"}
            
            if not collection_id:
                return {"code": 500, "message": "参数错误"}
            
            # 更新审核状态
            self.db['dataset_collections'].update_one(
                {"_id": ObjectId(collection_id)},
                {"$set": {"metadata.audit": "1", "forbid": False}}
            )
            
            # 审批通过的文档写入知识清单
            self._add_to_knowledge_list(collection_id)
            
            return {"code": 200, "data": []}
            
        except Exception as e:
            return {"code": 500, "message": str(e)}
    
    def update_dataset_data(self, data_list: List[Any]) -> Dict[str, Any]:
        """更新数据集数据"""
        try:
            if len(data_list) < 4:
                return {"code": 500, "message": "参数错误"}
            
            data_id = data_list[0]
            q = data_list[1]
            a = data_list[2] or ''
            indexes = data_list[3]
            
            if not data_id:
                return {"code": 500, "message": "参数错误"}
            
            # 处理索引数据
            new_indexes = []
            for index in indexes:
                if not index.get("text"):
                    continue
                if "editing" in index:
                    del index["editing"]
                new_indexes.append(index)
            
            # 移除图片HTML标签
            q = remove_image_html(q)
            
            # 构建更新数据
            update_data = {
                "dataId": data_id,
                "q": q,
                "a": a,
                "indexes": new_indexes
            }
            
            # 调用FastGPT API更新数据
            headers = settings.fastgpt_headers.copy()
            headers["Content-Type"] = "application/json"
            
            url = f"http://{settings.API_IP}:3000/api/core/dataset/data/update"
            response = requests.post(url, data=json.dumps(update_data), headers=headers)
            result = response.json()
            
            if result["code"] == 200:
                return {"code": 200}
            else:
                return {"code": result["code"], "message": str(result)}
                
        except Exception as e:
            return {"code": 500, "message": str(e)}
    
    def delete_qa_data(self, data_id: str) -> Dict[str, Any]:
        """删除QA数据"""
        try:
            if not data_id:
                return {"code": 500, "message": "参数错误"}
            
            url = f"http://{settings.API_IP}:3000/api/core/dataset/data/delete?id={data_id}"
            response = requests.delete(url, headers=settings.fastgpt_headers)
            result = response.json()
            
            if result["code"] == 200:
                return {"code": 200}
            else:
                return {"code": result["code"], "message": str(result)}
                
        except Exception as e:
            return {"code": 500, "message": str(e)}
    
    def _add_to_knowledge_list(self, collection_id: str):
        """将审核通过的文档添加到知识清单"""
        try:
            collection_obj = self.db["dataset_collections"].find_one({"_id": ObjectId(collection_id)})
            if not collection_obj:
                return
            
            dataset_id = str(collection_obj["datasetId"])
            create_time = collection_obj["createTime"]
            
            # 获取数据集名称
            dataset_name = knowledge_base_service.global_datasets_dic.get(dataset_id, "")
            collection_name = knowledge_base_service.global_collections_dic.get(collection_id, "")
            
            # 插入到知识清单
            self.db["files_list_wh"].insert_one({
                "datasetId": dataset_id,
                "datasetname": dataset_name,
                "collectionId": collection_id,
                "name": collection_name,
                "creatTime": create_time,
                "metadata": {}
            })
            
        except Exception as e:
            print(f"添加到知识清单发生错误: {e}")


# 创建全局服务实例
audit_service = AuditService()
