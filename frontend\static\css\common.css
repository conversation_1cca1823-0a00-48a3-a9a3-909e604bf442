/* 知识库管理系统 - 公共样式 */

/* ==================== 布局样式 ==================== */
.main-container {
  height: 100vh;
}

.nav-menu {
  height: calc(100% - 60px); /* 留出顶部空间 */
  margin-top: 60px; /* 下移导航栏 */
  border-right: none;
}

.toolbar {
  display: flex;
  justify-content: center; /* 居中对齐 */
  align-items: center;
  gap: 20px;
  padding: 15px 20px;
  background: white;
  height: 60px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  position: relative;
  z-index: 1;
}

.card-box {
  padding: 20px;
  height: calc(100vh - 160px);
  overflow-y: auto;
}

/* ==================== 文件卡片样式 ==================== */
.file-card {
  transition: all 0.3s;
  margin-bottom: 20px;
}

.file-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  transform: translateY(-5px);
}

.file-meta {
  font-size: 12px;
  color: #909399;
  margin: 8px 0;
}

.file-name {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 2.4em; /* 设置最小高度为两行 */
  line-height: 1.2em; /* 设置行高 */
}

.file-name2 {
  margin-top: 5px;
  font-size: 14px;
  color: #919397;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2em; /* 设置行高 */
}

/* ==================== 状态样式 ==================== */
.status-badge {
  position: absolute;
  border-radius: 12px;
  font-size: 12px;
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 红点和绿点 */
.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
}

.red {
  background-color: #ff4d4f; /* 红色 */
}

.green {
  background-color: #52c41a; /* 绿色 */
}

/* 未审核状态 */
.status-unreviewed {
  color: #ff4d4f; /* 红色文字 */
}

/* 已审核状态 */
.status-reviewed {
  color: #52c41a; /* 绿色文字 */
}

/* ==================== 表单样式 ==================== */
.el-form-item {
  margin-bottom: 20px;
}

.el-progress {
  margin-top: 20px;
}

/* ==================== 预览卡片样式 ==================== */
.preview-card-container {
  width: 90%; /* 卡片宽度为预览页面的60% */
  margin: 0 auto; /* 居中显示 */
  margin-top: 20px; /* 卡片之间的间距 */
}

.preview-card {
  position: relative;
  border: 1px solid #c6c9ce; /* 添加边框 */
  border-radius: 5px; /* 圆角 */
  transition: all 0.5s;
  padding: 10px; /* 内边距 */
  background-color: #f0f4ff;  /* 背景色 */
}

.preview-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  transform: translateY(-5px);
  border: #3370FF 1px solid; /* 边框颜色 */
  background-color: #d2d5e1fd;  /* 背景色 */
}

.preview-card img {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 保持比例，完整显示图片 */
}

.delete-icon {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: pointer;
  color: #F56C6C;
  font-size: 18px;
}

.delete-icon:hover {
  color: #ff0000;
}

/* ==================== 预览模态框样式 ==================== */
.preview-modal .el-dialog__body {
  padding: 20px;
}

.preview-modal .el-tabs__content {
  padding: 20px;
}

.preview-modal .el-textarea {
  width: 100%;
}

/* ==================== 数据索引样式 ==================== */
.index-item {
  margin-bottom: 15px;
}

.default-index, .custom-index {
  border: 1px solid #dcdfe6; /* 添加边框 */
  border-radius: 4px; /* 圆角 */
  padding: 10px; /* 内边距 */
  background-color: #f5f7fa; /* 背景色 */
  cursor: pointer; /* 鼠标悬停时显示手型 */
}

.index-content {
  display: flex;
  flex-direction: column;
  gap: 5px; /* 两行数据之间的间距 */
}

.custom-index .el-input {
  width: 100%;
}

.custom-index:hover {
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  border: #3370FF 1px solid; /* 边框颜色 */
  background-color: #d2d5e1fd;  /* 背景色 */
}

/* ==================== 标签页样式 ==================== */
/* 去掉 el-tabs 的底部横线 */
.custom-tabs .el-tabs__nav-wrap::after {
  display: none;
}

/* 调整新增索引按钮的样式 */
.custom-tabs .el-tab-pane {
  padding-top: 10px; /* 可以根据需要调整 */
}

.IDstyle {
  position: absolute;
  left: 10px;
  top: 10px;
  font-size: 12px;
  color: white;
}

.IDstyle:hover {
  color: #3370FF;
  display: block; /* 鼠标悬停时显示ID样式 */
}

/* ==================== 审核对话框样式 ==================== */
.audit-dialog {
  display: flex;
  flex-direction: column;
  max-height: 70vh;
}

.audit-dialog .el-dialog__body {
  padding: 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.audit-dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.same-files-table-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

/* 表格容器，确保内容可滚动 */
.same-files-table-container .el-table {
  flex: 1;
  overflow: auto;
}

/* 底部按钮固定 */
.audit-dialog-footer {
  padding: 10px 20px;
  border-top: 1px solid #ebeef5;
  text-align: right;
}

/* 调整表头固定 */
.same-files-table-container .el-table__header-wrapper {
  position: sticky;
  top: 0;
  z-index: 1;
  background: white;
}
