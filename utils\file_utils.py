"""
文件处理工具
提供文件相关的工具函数
"""

import re
from hashlib import md5
from urllib.parse import unquote, urlparse
from ..config.settings import settings


def get_token(username: str, password: str) -> str:
    """生成用户token"""
    return md5((username + '_' + password).encode()).hexdigest()


def get_image_html(q: str) -> str:
    """将Markdown图片格式转换为HTML格式"""
    pattern = r'!\[([^\]]*)\]\((/api/system/img/[^)]+\.(?:png|jpg|jpeg))\)'
    replacement = f'<image src="http://{settings.API_IP}:3000' + r'\2" alt="\1" loading="lazy" referrerpolicy="no-referrer"/>'
    return re.sub(pattern, replacement, q)


def remove_image_html(q: str) -> str:
    """将HTML图片格式还原为Markdown格式"""
    pattern = f'<image src="http://{settings.API_IP}:3000' + r'(/api/system/img/[^"]+\.(?:png|jpg|jpeg))" alt="([^"]*)" loading="lazy" referrerpolicy="no-referrer"/>'
    replacement = r'![\2](\1)'
    return re.sub(pattern, replacement, q)


def get_filename_from_url(url: str) -> str:
    """从URL中提取文件名"""
    parsed_url = urlparse(url)
    filename = unquote(parsed_url.path).split('/')[-1]
    return filename


def get_filename_from_headers(headers: dict) -> str:
    """从Content-Disposition头中提取文件名"""
    content_disposition = headers.get("Content-Disposition", "")
    if "filename=" in content_disposition:
        filename = content_disposition.split("filename=")[1].strip('"\'')
        return filename
    return None


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def validate_chunk_size(chunk_size: int, training_type: str) -> bool:
    """验证分块大小是否合法"""
    if training_type == 'chunk':
        return 100 <= chunk_size <= 1800
    elif training_type == 'qa':
        return 4000 <= chunk_size <= 16000
    return False
