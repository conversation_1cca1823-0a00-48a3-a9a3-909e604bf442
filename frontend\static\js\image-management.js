/**
 * 图片管理 - JavaScript
 */

// 图片管理应用组件
const ImageManagementApp = {
  init() {
    new Vue({
      el: '#app',
      data() {
        return {
          // 基础数据
          datasets: [],
          collections: [],
          selectedDataset: '',
          selectedCollection: '',
          loading: false,
          
          // 图片数据
          imageDataList: [],
          
          // 编辑对话框
          editDialogVisible: false,
          editForm: {
            id: '',
            q: '',
            a: ''
          },
          saving: false,
          
          // 图片预览
          previewDialogVisible: false,
          previewImageSrc: '',
          
          // 上传图片
          uploadDialogVisible: false,
          uploadFileList: [],
          uploading: false,
          
          // 替换图片
          replaceDialogVisible: false,
          replaceFileList: [],
          replacing: false,
          currentReplaceItem: null,
          currentReplaceIndex: -1,
          
          // 侧边栏状态
          activeMenu: '3',
          sfilesh: false
        };
      },
      
      mounted() {
        this.initializeApp();
      },
      
      methods: {
        /**
         * 初始化应用
         */
        async initializeApp() {
          await this.loadDatasets();
          if (this.datasets.length > 0) {
            this.selectedDataset = this.datasets[0].id;
            await this.loadCollections();
          }
        },
        
        /**
         * 加载数据集列表
         */
        async loadDatasets() {
          try {
            const response = await this.$api.get('/getDatasList/');
            if (response.code === 200) {
              this.datasets = response.data;
            }
          } catch (error) {
            console.error('加载数据集失败:', error);
            this.$showError('加载数据集失败');
          }
        },
        
        /**
         * 加载集合列表
         */
        async loadCollections() {
          if (!this.selectedDataset) return;
          
          try {
            const response = await this.$api.get('/getcollectionsList/', {
              datasetId: this.selectedDataset
            });
            if (response.code === 200) {
              this.collections = response.data;
              if (this.collections.length > 0) {
                this.selectedCollection = this.collections[0].id;
                await this.loadImageData();
              }
            }
          } catch (error) {
            console.error('加载集合列表失败:', error);
            this.$showError('加载集合列表失败');
          }
        },
        
        /**
         * 加载图片数据
         */
        async loadImageData() {
          if (!this.selectedCollection) return;
          
          this.loading = true;
          try {
            const response = await this.$api.get('/getDatasetsDatas_images/', {
              collectionId: this.selectedCollection
            });
            
            if (response.code === 200) {
              this.imageDataList = response.data.map(item => ({
                id: item[0],
                q: item[1],
                a: item[2] || '',
                processedQ: this.processImageContent(item[1]),
                images: this.extractImages(item[1])
              }));
            }
          } catch (error) {
            console.error('加载图片数据失败:', error);
            this.$showError('加载图片数据失败');
          } finally {
            this.loading = false;
          }
        },
        
        /**
         * 处理图片内容
         */
        processImageContent(content) {
          if (!content) return '';
          
          // 将Markdown图片格式转换为HTML
          const imagePattern = /!\[([^\]]*)\]\(([^)]+)\)/g;
          return content.replace(imagePattern, (match, alt, src) => {
            if (src.includes('/api/system/img/')) {
              return `<img src="http://171.43.138.237:3000${src}" alt="${alt}" style="max-width: 200px; height: auto; margin: 5px;" loading="lazy" referrerpolicy="no-referrer"/>`;
            }
            return match;
          });
        },
        
        /**
         * 提取图片信息
         */
        extractImages(content) {
          if (!content) return [];
          
          const images = [];
          const imagePattern = /!\[([^\]]*)\]\(([^)]+)\)/g;
          let match;
          
          while ((match = imagePattern.exec(content)) !== null) {
            const [fullMatch, alt, src] = match;
            if (src.includes('/api/system/img/')) {
              const picId = src.split('img/')[1].split('.')[0];
              images.push({
                alt: alt,
                src: `http://171.43.138.237:3000${src}`,
                picId: picId,
                originalMatch: fullMatch
              });
            }
          }
          
          return images;
        },
        
        /**
         * 编辑数据
         */
        editData(item) {
          this.editForm = {
            id: item.id,
            q: item.q,
            a: item.a
          };
          this.editDialogVisible = true;
        },
        
        /**
         * 保存编辑
         */
        async saveEdit() {
          this.saving = true;
          try {
            const response = await this.$api.post('/updateDatasetdatas/', {
              data: [this.editForm.id, this.editForm.q, this.editForm.a, []]
            });
            
            if (response.code === 200) {
              this.$showSuccess('保存成功');
              this.editDialogVisible = false;
              await this.loadImageData();
            } else {
              this.$showError('保存失败');
            }
          } catch (error) {
            console.error('保存失败:', error);
            this.$showError('保存失败');
          } finally {
            this.saving = false;
          }
        },
        
        /**
         * 预览图片
         */
        previewImage(image) {
          this.previewImageSrc = image.src;
          this.previewDialogVisible = true;
        },
        
        /**
         * 显示上传对话框
         */
        showUploadDialog() {
          this.uploadFileList = [];
          this.uploadDialogVisible = true;
        },
        
        /**
         * 处理图片选择
         */
        handleImageChange(file, fileList) {
          this.uploadFileList = fileList;
        },
        
        /**
         * 处理图片移除
         */
        handleImageRemove(file, fileList) {
          this.uploadFileList = fileList;
        },
        
        /**
         * 上传图片
         */
        async uploadImages() {
          if (this.uploadFileList.length === 0) {
            this.$showWarning('请先选择图片');
            return;
          }
          
          this.uploading = true;
          try {
            for (const file of this.uploadFileList) {
              const formData = new FormData();
              formData.append('file', file.raw || file);
              
              const response = await this.$api.upload('/uploadimage/', formData);
              if (response.code !== 200) {
                this.$showError(`图片 ${file.name} 上传失败`);
              }
            }
            
            this.$showSuccess('图片上传成功');
            this.uploadDialogVisible = false;
            await this.loadImageData();
          } catch (error) {
            console.error('上传图片失败:', error);
            this.$showError('上传图片失败');
          } finally {
            this.uploading = false;
          }
        },
        
        /**
         * 替换图片
         */
        replaceImage(item, imageIndex) {
          this.currentReplaceItem = item;
          this.currentReplaceIndex = imageIndex;
          this.replaceFileList = [];
          this.replaceDialogVisible = true;
        },
        
        /**
         * 处理替换图片选择
         */
        handleReplaceImageChange(file, fileList) {
          this.replaceFileList = fileList;
        },
        
        /**
         * 确认替换图片
         */
        async confirmReplace() {
          if (this.replaceFileList.length === 0) {
            this.$showWarning('请先选择新图片');
            return;
          }
          
          this.replacing = true;
          try {
            // 这里实现图片替换逻辑
            this.$showSuccess('图片替换成功');
            this.replaceDialogVisible = false;
            await this.loadImageData();
          } catch (error) {
            console.error('替换图片失败:', error);
            this.$showError('替换图片失败');
          } finally {
            this.replacing = false;
          }
        },
        
        /**
         * 删除图片
         */
        async deleteImage(item, imageIndex) {
          try {
            await this.$confirm('确定要删除这张图片吗？', '确认删除');
            
            // 这里实现图片删除逻辑
            this.$showSuccess('图片删除成功');
            await this.loadImageData();
          } catch (error) {
            if (error !== 'cancel') {
              console.error('删除图片失败:', error);
              this.$showError('删除图片失败');
            }
          }
        },
        
        /**
         * 处理菜单选择
         */
        handleMenuSelect(index) {
          if (index === '2') {
            window.router.navigate('knowledge-base');
          } else if (index === '1') {
            window.router.navigate('chat-logs');
          } else if (index === '3') {
            this.activeMenu = index;
          } else if (index === '4') {
            window.router.navigate('audit');
          }
        }
      }
    });
  }
};

// 导出到全局
window.ImageManagementApp = ImageManagementApp;
