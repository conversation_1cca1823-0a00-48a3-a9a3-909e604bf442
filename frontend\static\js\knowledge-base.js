/**
 * 知识库管理系统 - 主要JavaScript文件
 */

// 确保所有模块都已加载
document.addEventListener('DOMContentLoaded', function() {
  // 加载所有模块
  const modules = [
    '/frontend/static/js/modules/data-manager.js',
    '/frontend/static/js/modules/file-manager.js',
    '/frontend/static/js/modules/auth-manager.js'
  ];

  // 动态加载模块
  Promise.all(modules.map(src => {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  })).then(() => {
    // 所有模块加载完成后初始化Vue应用
    initVueApp();
  }).catch(error => {
    console.error('模块加载失败:', error);
    // 即使模块加载失败也尝试初始化应用
    initVueApp();
  });
});

function initVueApp() {
  new Vue({
    el: '#app',
    data() {
      return {
        // 基础状态
        activeMenu: '2',
        loading: false,
        
        // 搜索和过滤
        searchKey: '',
        dateRange: [],
        selectedLibrary: '',
        filterStatus: '',
        
        // 数据
        files: [],
        filteredFiles: [],
        libraries: [],
        serverIP: '',
        
        // 认证相关
        sfilesh: false,
        token: '',
        loginDialogVisible: false,
        loginForm: {
          username: '',
          password: ''
        },
        loginLoading: false,
        
        // 文件上传
        importDialogVisible: false,
        uploadForm: {
          selectedLibrary: '',
          processingMethod: 'chunk',
          processingParam: 'auto',
          chunkLength: 512,
          customSeparator: ''
        },
        selectedFiles: [],
        uploadProgress: 0,
        uploading: false,
        
        // 文件预览
        previewDialogVisible: false,
        previewData: { name: '', q: [], audit: '1' },
        activeTab: 'content',
        saving: false,
        
        // 删除确认
        deleteDialogVisible: false,
        currentFile: null,
        
        // 审核相关
        auditDialogVisible: false,
        sameFiles: [],
        auditing: false,
        
        // 文件信息
        fileInfoDialogVisible: false,
        fileInfoForm: {
          型号: '',
          名称: '',
          售前售后: '',
          可接入软件: ''
        },
        currentUploadingFile: null,
        
        // 日期选择器配置
        pickerOptions: {
          shortcuts: [{
            text: '本周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setDate(start.getDate() - start.getDay() + 1);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '本月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setDate(1);
              picker.$emit('pick', [start, end]);
            }
          }]
        }
      };
    },
    
    computed: {
      // 计算属性可以在这里添加
    },
    
    mounted() {
      this.initializeApp();
    },
    
    methods: {
      /**
       * 初始化应用
       */
      async initializeApp() {
        // 初始化认证状态
        const authState = AuthManager.initAuthState();
        this.sfilesh = authState.hasPermission;
        this.token = authState.token;
        
        // 加载数据
        await this.loadLibraries();
        await this.loadFiles();
      },
      
      /**
       * 加载知识库列表
       */
      async loadLibraries() {
        try {
          const response = await DataManager.getDatasetsList();
          if (response.code === 200) {
            this.libraries = response.data;
          }
        } catch (error) {
          console.error('加载知识库列表失败:', error);
        }
      },
      
      /**
       * 加载文件列表
       */
      async loadFiles(params = {}) {
        this.loading = true;
        try {
          const response = await DataManager.getCollectionListInfo(params);
          if (response.code === 200) {
            this.files = response.data;
            this.filteredFiles = this.files;
            this.serverIP = response.serverIP || '';
          }
        } catch (error) {
          console.error('加载文件失败:', error);
          this.$showError('加载文件失败');
        } finally {
          this.loading = false;
        }
      },
      
      /**
       * 处理菜单选择
       */
      handleMenuSelect(index) {
        if (index === '2') {
          this.loadFiles();
          this.activeMenu = index;
        } else if (index === '1') {
          window.open(`http://${this.serverIP}/h5_log/`, '_blank');
        } else if (index === '3') {
          window.open(`http://${this.serverIP}/h5_images/`, '_blank');
        } else if (index === '4') {
          window.open(`http://${this.serverIP}/h5_images_audit_wh/`, '_blank');
        }
      },
      
      /**
       * 处理搜索
       */
      handleSearch() {
        this.filteredFiles = this.files.filter(file =>
          file.name.toLowerCase().includes(this.searchKey.toLowerCase())
        );
      },
      
      /**
       * 处理查询
       */
      handleQuery() {
        const params = {
          keyword: this.searchKey,
          startDate: this.dateRange[0] ? dayjs(this.dateRange[0]).format('YYYY-MM-DD') : '',
          endDate: this.dateRange[1] ? dayjs(this.dateRange[1]).format('YYYY-MM-DD') : '',
          status: this.filterStatus,
          dataset: this.selectedLibrary,
        };
        console.log('查询参数:', params);
        this.loadFiles(params);
      },
      
      /**
       * 处理登录点击
       */
      handleLoginClick(action) {
        if (action === 'logon') {
          this.loginDialogVisible = true;
        } else {
          AuthManager.logout();
          this.sfilesh = false;
          this.token = '';
          this.loadFiles();
        }
      },
      
      /**
       * 处理登录
       */
      async handleLogin() {
        if (!AuthManager.validateLoginForm(this.loginForm.username, this.loginForm.password)) {
          return;
        }
        
        this.loginLoading = true;
        try {
          const response = await AuthManager.login(this.loginForm.username, this.loginForm.password);
          
          if (response.code === 200) {
            this.$showSuccess('登录成功');
            this.sfilesh = true;
            this.token = response.token;
            this.loginDialogVisible = false;
            
            // 保存认证状态
            AuthManager.setToken(response.token);
            AuthManager.setPermissionStatus(true);
            
            this.loadFiles();
          } else {
            this.$showError('登录失败：' + response.message);
          }
        } catch (error) {
          console.error('登录失败:', error);
          this.$showError('登录失败');
        } finally {
          this.loginLoading = false;
        }
      },
      
      /**
       * 打开导入对话框
       */
      handleOpenImport() {
        this.importDialogVisible = true;
      },
      
      /**
       * 重置上传表单
       */
      resetUploadForm() {
        this.uploadForm = {
          selectedLibrary: '',
          processingMethod: 'chunk',
          processingParam: 'auto',
          chunkLength: 512,
          customSeparator: ''
        };
        this.selectedFiles = [];
        this.uploadProgress = 0;
        this.uploading = false;
      },
      
      /**
       * 处理文件变化
       */
      handleFileChange(file, fileList) {
        this.selectedFiles = fileList;
      },
      
      /**
       * 处理文件移除
       */
      handleRemove(file, fileList) {
        this.selectedFiles = fileList;
      },

      /**
       * 上传所有文件
       */
      async uploadAllFiles() {
        if (this.selectedFiles.length === 0) {
          this.$showWarning('请先选择文件');
          return;
        }

        if (!FileManager.validateBeforeUpload(
          null,
          this.uploadForm.selectedLibrary,
          this.uploadForm.processingParam,
          this.uploadForm.processingMethod,
          this.uploadForm.chunkLength
        )) {
          return;
        }

        this.uploading = true;
        this.uploadProgress = 0;

        try {
          const processParams = FileManager.buildProcessParams(
            this.uploadForm.processingMethod,
            this.uploadForm.processingParam,
            this.uploadForm.chunkLength,
            this.uploadForm.customSeparator
          );

          let completed = 0;
          const total = this.selectedFiles.length;

          for (const file of this.selectedFiles) {
            try {
              await FileManager.uploadFile(
                file.raw || file,
                this.uploadForm.selectedLibrary,
                processParams,
                (progress) => {
                  // 单个文件进度更新
                  const overallProgress = Math.round(((completed + progress / 100) / total) * 100);
                  this.uploadProgress = overallProgress;
                }
              );

              completed++;
              this.uploadProgress = Math.round((completed / total) * 100);

            } catch (error) {
              console.error(`文件 ${file.name} 上传失败:`, error);
              this.$showError(`文件 ${file.name} 上传失败`);
            }
          }

          if (completed === total) {
            this.$showSuccess('所有文件上传成功');
            this.importDialogVisible = false;
            this.resetUploadForm();
            this.loadFiles();
          } else {
            this.$showWarning(`${completed}/${total} 个文件上传成功`);
          }

        } catch (error) {
          console.error('批量上传失败:', error);
          this.$showError('批量上传失败');
        } finally {
          this.uploading = false;
        }
      },

      /**
       * 预览文件
       */
      async previewFile(file) {
        this.loading = true;
        try {
          const response = await DataManager.getDatasetDatas(file.id);
          if (response.code === 200) {
            this.previewData = response.data;
            this.previewDialogVisible = true;
          }
        } catch (error) {
          console.error('预览文件失败:', error);
          this.$showError('预览文件失败');
        } finally {
          this.loading = false;
        }
      },

      /**
       * 删除文件
       */
      deleteFile(file) {
        this.currentFile = file;
        this.deleteDialogVisible = true;
      },

      /**
       * 确认删除
       */
      async confirmDelete() {
        this.loading = true;
        try {
          const response = await DataManager.deleteCollection(this.currentFile.id);
          if (response.code === 200) {
            this.$showSuccess('删除成功');
            this.loadFiles();
          } else {
            this.$showError('删除失败');
          }
        } catch (error) {
          console.error('删除文件失败:', error);
          this.$showError('删除文件失败');
        } finally {
          this.loading = false;
          this.deleteDialogVisible = false;
        }
      },

      /**
       * 审核文件
       */
      async auditFile(file) {
        this.currentFile = file;

        // 获取相同型号的文件
        try {
          const response = await DataManager.getSameNameFiles(file.cpxh, file.id);
          if (response.code === 200) {
            this.sameFiles = response.data;
            this.auditDialogVisible = true;
          }
        } catch (error) {
          console.error('获取相同型号文件失败:', error);
          this.$showError('获取相同型号文件失败');
        }
      },

      /**
       * 确认审核
       */
      async confirmAudit() {
        this.auditing = true;
        try {
          const response = await ApiClient.post('/auditCollection/', {
            collectionId: this.currentFile.id,
            token: this.token
          });

          if (response.code === 200) {
            this.$showSuccess('审核成功');
            this.auditDialogVisible = false;
            this.loadFiles();
          } else {
            this.$showError('审核失败：' + response.message);
          }
        } catch (error) {
          console.error('审核失败:', error);
          this.$showError('审核失败');
        } finally {
          this.auditing = false;
        }
      },

      /**
       * 保存预览数据
       */
      async savePreviewData() {
        this.saving = true;
        try {
          // 这里需要实现保存逻辑
          this.$showSuccess('保存成功');
          this.previewDialogVisible = false;
        } catch (error) {
          console.error('保存失败:', error);
          this.$showError('保存失败');
        } finally {
          this.saving = false;
        }
      },

      /**
       * 添加新索引
       */
      addNewIndex(indexes) {
        indexes.push({
          text: '',
          editing: true
        });
      },

      /**
       * 删除QA项目
       */
      async deleteQAItem(id, index) {
        try {
          await this.$confirm('确定要删除这个QA项目吗？', '确认删除');

          const response = await DataManager.deleteQA(id);
          if (response.code === 200) {
            this.previewData.q.splice(index, 1);
            this.$showSuccess('删除成功');
          }
        } catch (error) {
          if (error !== 'cancel') {
            console.error('删除QA项目失败:', error);
            this.$showError('删除失败');
          }
        }
      },

      /**
       * 格式化时间
       */
      formatTime(row, column, cellValue) {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss');
      }
    }
  });
}

// 导出初始化函数
window.initVueApp = initVueApp;
