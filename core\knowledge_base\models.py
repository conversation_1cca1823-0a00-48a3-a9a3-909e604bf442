"""
知识库数据模型
定义知识库相关的数据结构
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from datetime import datetime


class DatasetModel(BaseModel):
    """数据集模型"""
    id: str
    name: str
    intro: Optional[str] = None
    parent_id: Optional[str] = None
    create_time: Optional[datetime] = None


class CollectionModel(BaseModel):
    """集合模型"""
    id: str
    dataset_id: str
    name: str
    type: str
    metadata: Dict[str, Any] = {}
    create_time: Optional[datetime] = None
    forbid: bool = False


class DataModel(BaseModel):
    """数据模型"""
    id: str
    collection_id: str
    q: str
    a: Optional[str] = None
    indexes: List[Dict[str, Any]] = []
    chunk_index: Optional[int] = None


class FileInfoModel(BaseModel):
    """文件信息模型"""
    型号: str = "未知"
    名称: str = "未知"
    售前售后: str = "未知"
    可接入软件: str = "未知"


class UploadRequestModel(BaseModel):
    """上传请求模型"""
    training_type: str
    chunk_size: int
    chunk_splitter: str = ""
    processing_param: str = "auto"
    file_info: Optional[FileInfoModel] = None
