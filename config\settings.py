"""
应用程序设置
从原config.py迁移的配置信息
"""

import os
from typing import List
from bson.objectid import ObjectId


class Settings:
    """应用程序设置类"""
    
    # 服务器配置
    SERVER_IP: str = "**************"
    API_IP: str = "**************"
    TEMPLATES_IP: str = "*************:8009"
    
    # MongoDB配置
    MONGODB_USERNAME: str = "myusername"
    MONGODB_PASSWORD: str = "mypassword"
    MONGODB_HOST: str = SERVER_IP
    MONGODB_PORT: int = 27017
    MONGODB_DATABASE: str = "fastgpt"
    MONGODB_AUTH_SOURCE: str = "admin"
    
    @property
    def mongodb_url(self) -> str:
        """获取MongoDB连接URL"""
        return (f"mongodb://{self.MONGODB_USERNAME}:{self.MONGODB_PASSWORD}@"
                f"{self.MONGODB_HOST}:{self.MONGODB_PORT}/{self.MONGODB_DATABASE}"
                f"?authSource={self.MONGODB_AUTH_SOURCE}&directConnection=true")
    
    # FastGPT API配置
    FASTGPT_AUTHORIZATION: str = "Bearer fastgpt-e5Lf0kDHo9kXA29cgNVroVWfdNhlw2Csx3ErmS0A0ISfhJyh1EL6eP1HNEqGIX4o"
    
    @property
    def fastgpt_headers(self) -> dict:
        """获取FastGPT请求头"""
        return {"Authorization": self.FASTGPT_AUTHORIZATION}
    
    # 认证配置
    BASE_USERNAME: str = "admin"
    BASE_PASSWORD: str = "whyf2025"
    
    # 智谱AI配置
    ZHIPU_API_KEY: str = "d3e441ee4a536dd76c88a404ad8b05e4.t97wBPQIa5GuFT4y"
    
    # 知识库配置
    PARENT_DATASET_ID: str = "675f86de7bb3c4679814c738"
    BEIJING_DATASET_ID: str = "6739d0b48e3e1b37c294c883"
    BIAOSHU_DATASET_ID: str = "6777ca0d9f5a5d92bd1c6fac"


# 创建全局设置实例
settings = Settings()
