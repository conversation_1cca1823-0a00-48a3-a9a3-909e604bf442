<!-- 文件上传对话框组件 -->
<el-dialog
  title="导入数据"
  :visible.sync="importDialogVisible"
  width="600px"
  :close-on-click-modal="false"
  @close="resetUploadForm">
  
  <el-form :model="uploadForm" ref="uploadForm" label-width="120px">
    <el-form-item label="选择知识库" prop="selectedLibrary" :rules="[{required: true, message: '请选择知识库', trigger: 'change'}]">
      <el-select v-model="uploadForm.selectedLibrary" placeholder="请选择知识库" style="width: 100%;">
        <el-option
          v-for="kb in libraries"
          :key="kb.id"
          :label="kb.name"
          :value="kb.id">
        </el-option>
      </el-select>
    </el-form-item>
    
    <el-form-item label="处理方式">
      <el-radio-group v-model="uploadForm.processingMethod">
        <el-radio label="chunk">分块模式</el-radio>
        <el-radio label="qa">问答模式</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <el-form-item label="参数设置">
      <el-radio-group v-model="uploadForm.processingParam">
        <el-radio label="auto">自动</el-radio>
        <el-radio label="custom">自定义</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <el-form-item label="分块长度" v-if="uploadForm.processingParam === 'custom'">
      <el-input-number
        v-model="uploadForm.chunkLength"
        :min="uploadForm.processingMethod === 'chunk' ? 100 : 4000"
        :max="uploadForm.processingMethod === 'chunk' ? 1800 : 16000"
        style="width: 100%;">
      </el-input-number>
    </el-form-item>
    
    <el-form-item label="自定义分隔符" v-if="uploadForm.processingParam === 'custom'">
      <el-input v-model="uploadForm.customSeparator" placeholder="可选，留空使用默认分隔符"></el-input>
    </el-form-item>
    
    <el-form-item label="选择文件">
      <el-upload
        ref="upload"
        :auto-upload="false"
        :on-change="handleFileChange"
        :on-remove="handleRemove"
        :file-list="selectedFiles"
        multiple
        drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">支持多文件上传</div>
      </el-upload>
    </el-form-item>
  </el-form>
  
  <!-- 上传进度 -->
  <el-progress
    v-if="uploadProgress > 0"
    :percentage="uploadProgress"
    :status="uploadProgress === 100 ? 'success' : ''"
    style="margin-top: 20px;">
  </el-progress>
  
  <div slot="footer" class="dialog-footer">
    <el-button @click="importDialogVisible = false">取消</el-button>
    <el-button type="primary" @click="uploadAllFiles" :loading="uploading" :disabled="selectedFiles.length === 0">
      开始上传
    </el-button>
  </div>
</el-dialog>
