<!-- 文件卡片组件 -->
<el-card class="file-card">
  <div class="file-header" style="font-size: 10px;display:none;">
    {{file.datasetId}} {{file.id}}
  </div>
  
  <div style="position: relative;">
    <!-- 状态显示 -->
    <div v-if="file.audit == 0" class="status-badge status-unreviewed">
      <span class="dot red"></span>未审核
    </div>
    <div v-else class="status-badge status-reviewed">
      <span class="dot green"></span>已审核
    </div>
  </div>
  
  <div style="text-align:center">
    <i class="el-icon-document" style="font-size:36px;color:#409EFF"></i>
    
    <el-tooltip class="item" effect="dark" :content="file.name" placement="top">
      <div class="file-name">{{ file.name }}</div>
    </el-tooltip>
    
    <div class="file-name2">{{file.datasetname}}</div>
    
    <div class="file-meta">
      <div>{{ file.size | formatSize }}</div>
      <div>{{ file.time | formatTime }}</div>
    </div>
    
    <div>
      <el-button type="text" @click="previewFile(file)">预览</el-button>
      <el-button type="text" @click="auditFile(file)" v-if="sfilesh && file.audit == 0">审核</el-button>
      <el-button type="text" style="color:#F56C6C" @click="deleteFile(file)" v-if="file.audit == 0">删除</el-button>
    </div>
  </div>
</el-card>
