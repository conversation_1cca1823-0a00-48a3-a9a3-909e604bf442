import os
import re
import json
import requests
from hashlib import md5
from zhipuai import ZhipuAI
from urllib.parse import unquote, urlparse, quote
from pymongo import MongoClient
from bson.objectid import ObjectId
from datetime import datetime, timezone
from fastapi.responses import HTMLResponse
from fastapi import HTTPException,Request, HTTPException,File,Form, UploadFile
from config import serverIP,apiIP,templatesIP,baseusername,basepassword,dataids_list,headers,tishici_dict,zhishiku_sm_dict,zhishiku_ss_dict,zhipukey,system_prompt,bs_prompt,init_monogdb

client = ZhipuAI(api_key=zhipukey)
global_datasets_dic = {}
global_datasets_collections_dic = {}

def get_token(u,p):
    return md5((u+'_'+p).encode()).hexdigest()

def get_global_data():
    global global_datasets_dic
    global global_datasets_collections_dic
    global_datasets_dic = {}
    global_datasets_collections_dic = {}
    db = init_monogdb()
    dbdata = db['datasets'].find({"_id": {"$in": dataids_list}})
    global_datasets_dic = {str(d["_id"]):d["name"] for d in dbdata}
    collections = db['dataset_collections'].find({"datasetId": {"$in": dataids_list}})
    global_datasets_collections_dic = {str(d["_id"]):d["name"] for d in collections}


async def getDatasList():
    try:
        global global_datasets_dic
        global global_datasets_collections_dic
        if not global_datasets_dic or not global_datasets_collections_dic:
            get_global_data()
        datasets_dic = [{"id":k,"name":v} for k,v in global_datasets_dic.items()]
        return {"code":200,"data":datasets_dic}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def sync_beijng_api(file_content,content_type,qaPrompt,filename,parentId):
    try:
        data = {
                "datasetId": "6739d0b48e3e1b37c294c883",  # 知识库ID
                "parentId": parentId,  # 父级ID
                "trainingType": "qa",  # 训练模式
                "chunkSize": 4000,  # chunk长度
                "chunkSplitter": "",  # 自定义分割符号
                "qaPrompt": qaPrompt  # qa拆分提示词
            }
        data_json = json.dumps(data)
        files = {"file": (quote(filename), file_content, content_type),"data": (None, data_json, "application/json")}
        response = requests.post(f"http://{apiIP}:3000/api/core/dataset/collection/create/localFile",headers=headers,files=files).json()
    except Exception as e:
        print(f"发生了一个错误2223: {e}")


def get_indexes(cotent,prompt):
    try:
        completion = client.chat.completions.create(
            model="glm-4-air",
            messages=[{"role": "system", "content": prompt},{"role": "user", "content": cotent}],
            temperature=0.3,
            response_format={"type": "json_object"}
        )
        chinese_blocks = json.loads(completion.choices[0].message.content)
        gjzsd = chinese_blocks["text"]
        gjc = chinese_blocks["text2"]
        return gjzsd,gjc
    except Exception as e:
        print(f"发生了一个错误3: {e}")
    return "",""

def sync_indexs():
    try:
        db = init_monogdb()
        collections = list(db["dataset_collections"].find({"metadata.isindex": "0"}))
        for coll in collections:
            metadata = coll.get("metadata",{})
            if metadata.get("source") == "bs":
                prompt = bs_prompt
            else:
                prompt = system_prompt
            dataids_list = list(db['dataset_datas'].find({"collectionId": coll["_id"]}).sort({"chunkIndex": 1 }))
            # 如果已经有数据正在处理，则跳过
            if dataids_list and db["dataset_trainings"].count_documents({"collectionId": coll["_id"]}) > 0:
                continue
            for data in dataids_list:
                try:
                    newindexs = get_indexs(data["indexes"],1)
                    data["q"] = get_image(data["q"])

                    name = "文件名为："+coll["name"]
                    if coll.get("cpxh"):
                        name += "，产品型号为："+coll["cpxh"]
                    if coll.get("cpmc"):
                        name += "，产品名称为："+coll["cpmc"]

                    content = """%s 中的片段内容为 %s"""%(name,data['q'])

                    gjzsd,gjc = get_indexes(content,prompt)
                    if not gjzsd and not gjc:
                        continue
                    newindexs.append({"text": gjzsd})
                    newindexs.append({"text": gjc})
                    if coll.get("cpxh") and coll.get("cpmc"):
                        newindexs.append({"text": f"{coll['cpxh']}{coll['cpmc']}"})

                    data = {"dataId": str(data["_id"]),"q": data['q'],"a": data['a'],"indexes": newindexs}
                    url = f'http://{apiIP}:3000/api/core/dataset/data/update'
                    headers = {'Authorization': 'Bearer fastgpt-e5Lf0kDHo9kXA29cgNVroVWfdNhlw2Csx3ErmS0A0ISfhJyh1EL6eP1HNEqGIX4o','Content-Type': 'application/json'}
                    response = requests.put(url, headers=headers, data=json.dumps(data))
                except Exception as e:
                    print(f"发生了一个错误2: {e}")
            db['dataset_collections'].update_one({"_id": coll["_id"]},{"$set":{"metadata.isindex":"1"}})
    except Exception as e:
        print(f"发生了一个错误3: {e}")


async def getFileInfo(filename: str):
    try:
        # 使用智谱AI提取文件信息
        prompt = """
            - Role: 信息提取与处理专家
            - Background: 用户需要从文件名称中提取产品信息，包括型号、名称、售前/售后类型、可接入软件等，并以JSON字符串的形式返回。
            - Profile: 你是一位精通文本处理和信息提取的专家，擅长从复杂的文本中快速提取关键信息，并能够准确地将其格式化为所需的结构。
            - Skills: 你具备强大的文本解析能力，能够识别和提取特定模式的信息，同时精通JSON格式的生成和处理。
            - Goals: 从文件名称中准确提取产品信息，并以JSON字符串的形式返回。
            - Constrains: 提取的信息必须准确无误，且返回的JSON格式必须符合规范。
            - OutputFormat: JSON字符串，例如 {"型号":"xface600", "名称":"门禁机", "售前售后":"售前", "可接入软件":"万傲瑞达V6600"}
            - Workflow:
            1. 接收文件名称作为输入。
            2. 分析文件名称，提取其中的产品信息。
            3. 将提取的信息格式化为JSON字符串并返回。
            - Examples:
            - 例子1：文件名称 "xface600使用说明书"
                输出：{"型号":"xface600", "名称":"门禁机", "售前售后":"售前", "可接入软件":"万傲瑞达V6600"}
            - 例子2：文件名称 "cm500功能参数介绍及使用说明"
                输出：{"型号":"cm500", "名称":"考勤机", "售前售后":"售前", "可接入软件":"百傲瑞达3.0"}
            - 例子3：文件名称 "pro3000用户手册"
                输出：{"型号":"pro3000", "名称":"门禁机", "售前售后":"售后", "可接入软件":"万傲瑞达V6600"}
            - Initialization: 在第一次对话中，请直接输出以下：您好！我将帮助您从文件名称中提取产品信息并以JSON格式返回。请提供需要处理的文件名称。
        """
        completion = client.chat.completions.create(
            model="glm-4-air",
            messages=[
                {"role": "system", "content": prompt},
                {"role": "user", "content": filename}
            ],
            temperature=0.3,
            response_format={"type": "json_object"}
        )

        response_text = completion.choices[0].message.content

        # 尝试提取JSON字符串
        try:
            file_info = json.loads(response_text)
            return {"code": 200, "data": file_info}
        except Exception as e:
            # JSON解析失败，返回默认值
            default_info = {
                "型号": "未知",
                "名称": "未知",
                "售前售后": "未知",
                "可接入软件": "未知"
            }
            return {"code": 200, "data": default_info}
    except Exception as e:
        # 接口调用失败，返回默认值
        default_info = {
            "型号": "未知",
            "名称": "未知",
            "售前售后": "未知",
            "可接入软件": "未知"
        }
        return {"code": 200, "data": default_info}

def get_cpxh(i,metadata):
    try:
        prompt = """
            - Role: 信息提取与处理专家
            - Background: 用户需要从文件名称中提取产品型号，并以JSON字符串的形式返回。这通常是为了快速整理和分类文件，提高工作效率。
            - Profile: 你是一位精通文本处理和信息提取的专家，擅长从复杂的文本中快速提取关键信息，并能够准确地将其格式化为所需的结构。
            - Skills: 你具备强大的文本解析能力，能够识别和提取特定模式的信息，同时精通JSON格式的生成和处理。
            - Goals: 从文件名称中准确提取产品型号，并以JSON字符串的形式返回。
            - Constrains: 提取的产品型号必须准确无误，且返回的JSON格式必须符合规范。
            - OutputFormat: JSON字符串，例如 {"产品型号":"xface600"}
            - Workflow:
            1. 接收文件名称作为输入。
            2. 分析文件名称，提取其中的产品型号。
            3. 将提取的产品型号格式化为JSON字符串并返回。
            - Examples:
            - 例子1：文件名称 "xface600使用说明书"
                输出：{"产品型号":"xface600"}
            - 例子2：文件名称 "cm500功能参数介绍及使用说明"
                输出：{"产品型号":"cm500"}
            - 例子3：文件名称 "pro3000用户手册"
                输出：{"产品型号":"pro3000"}
            - Initialization: 在第一次对话中，请直接输出以下：您好！我将帮助您从文件名称中提取产品型号并以JSON格式返回。请提供需要处理的文件名称。
        """
        completion = client.chat.completions.create(
            model="glm-4-air",
            messages=[{"role": "system", "content": prompt},{"role": "user", "content": i["name"]}],
            temperature=0.3,
            response_format={"type": "json_object"}
        )
        chinese_blocks = json.loads(completion.choices[0].message.content)
        cpxh = chinese_blocks["产品型号"]
        metadata["cpxh"] = cpxh
    except Exception as e:
        print(f"发生了一个错误: {e}")
    return metadata

async def uploadfiles(file: UploadFile = File(...), type: str = Form(...), data: str = Form(...)):
    try:
        global global_datasets_collections_dic,global_datasets_dic
        if type not in global_datasets_dic:
            get_global_data()
        datasetName = global_datasets_dic[type]
        file_content = await file.read()
        process_params = json.loads(data)
        trainingType = process_params["trainingType"]
        dataset_sm = zhishiku_sm_dict.get(datasetName,"")
        qaPrompt = tishici_dict.get(datasetName,"")
        qaPrompt = '''知识库名称是%s，知识库说明是%s,文件名称是%s。
                Role: 知识库内容优化专家和问答生成工程师
                - Background: 用户需要对知识库内容进行优化，以便生成与文件内容强相关且准确的问答对。知识库包含产品介绍、功能、使用说明、接线图、部署和问题处理等多方面信息，用户期望从多维度生成最多50个问题，且问题和答案需避免重复和类似，确保内容的准确性和多样性。
                - Profile: 你是一位精通知识库管理和问答系统构建的专家，对文本内容的拆分、理解和重组有着丰富的经验。你擅长从复杂的产品文档中提取关键信息，并以清晰、准确的方式生成问答对，确保每个问题都能精准地对应文件内容的某个方面。
                - Skills: 你具备文本分析、信息提取、自然语言处理和问答系统设计的能力，能够高效地从产品文档中识别出不同维度的内容，并生成多样化的问答对，同时确保答案的完整性和准确性。
                - Goals: 根据知识库说明、文件名称和文件内容，生成最多50个与文件内容强相关的问答对，确保问题和答案的准确性和多样性，避免重复和类似内容。
                - Constrains: 生成的问答对需紧密围绕文件内容，确保答案详细完整且尽可能保留原文描述，问题需明确且具有针对性，避免模糊和重复。
                - OutputFormat: 以问答对的形式输出，每个问题前需带上产品型号或产品名称，问题和答案需紧密相关且准确。
                - Workflow:
                1. 详细阅读和理解知识库说明、文件名称及文件内容，识别其中的关键信息和不同维度的内容。
                2. 根据文件内容的不同部分（如产品介绍、功能、使用说明等），生成针对性的问题，并确保问题的多样性和准确性。
                3. 对应每个问题，从文件内容中提取详细且准确的答案，尽可能保留原文描述，确保答案的完整性和准确性。
                以下是知识库背景和示例： %s
                '''%(datasetName,dataset_sm,file.filename,qaPrompt)
        qaPromptTag = False
        if trainingType == "qa" and process_params["processingParam"] == "custom":
            qaPromptTag = True
        data = {
            "datasetId": type,  # 知识库ID
            "parentId": None,  # 父级ID
            "trainingType": trainingType,  # 训练模式
            "chunkSize": process_params["chunkSize"],  # chunk长度
            "chunkSplitter": process_params["chunkSplitter"],  # 自定义分割符号
            "qaPrompt": qaPrompt if qaPromptTag else "",  # qa拆分提示词
            "metadata": {"source": "api","audit":"0","size":file.size}  # 元数据
        }

        # 添加文件信息到metadata
        if process_params.get("fileInfo"):
            file_info = process_params.get("fileInfo")
            data["metadata"]["型号"] = file_info.get("型号", "未知")
            data["metadata"]["名称"] = file_info.get("名称", "未知")
            data["metadata"]["售前售后"] = file_info.get("售前售后", "未知")
            data["metadata"]["可接入软件"] = file_info.get("可接入软件", "未知")

        data_json = json.dumps(data)
        files = {"file": (quote(file.filename), file_content, file.content_type),"data": (None, data_json, "application/json")}
        response = requests.post(f"http://{apiIP}:3000/api/core/dataset/collection/create/localFile",headers=headers,files=files).json()
        # 检查响应状态
        if response["code"] == 200:
            # 更新全局变量 global_datasets_collections_dic
            global_datasets_collections_dic[str(response["data"]["collectionId"])] = file.filename
            collectionId = ObjectId(response["data"]["collectionId"])
            db = init_monogdb()
            collection = list(db['dataset_collections'].find({"_id": collectionId}))[0]
            metadata = collection["metadata"]
            metadata = get_cpxh(collection,metadata)
            metadata["source"] = "api"
            metadata["audit"] = "0"
            metadata["isindex"] = "0"
            metadata["size"] = file.size

            # 添加文件信息到metadata
            if process_params.get("fileInfo"):
                file_info = process_params.get("fileInfo")
                metadata["型号"] = file_info.get("型号", "未知")
                metadata["名称"] = file_info.get("名称", "未知")
                metadata["售前售后"] = file_info.get("售前售后", "未知")
                metadata["可接入软件"] = file_info.get("可接入软件", "未知")

            db['dataset_collections'].update_one({"_id":collectionId},{"$set":{"metadata":metadata,"forbid": True}})
            parentId = zhishiku_ss_dict.get(datasetName,'')
            sync_beijng_api(file_content,file.content_type,qaPrompt,file.filename,parentId)
            # sync_indexs()  # 用定时任务去执行
            return {"code": 200}
        else:
            raise HTTPException(status_code=response["code"], detail=str(response))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def getSameNameFiles(cpxh: str, excludeId: str):
    try:
        db = init_monogdb()
        query = {"_id": {"$ne": ObjectId(excludeId)},"datasetId": {"$in": dataids_list},"type": "file","metadata.cpxh": {"$regex": cpxh, "$options": "i"}}
        collections = db['dataset_collections'].find(query).sort("createTime", -1)
        result = []
        for collection in collections:
            result.append({
                "id": str(collection["_id"]),
                "datasetId": str(collection["datasetId"]),
                "name": collection["name"],
                "time": collection["createTime"],
                "audit": collection["metadata"].get("audit","1")
            })
        return {"code": 200, "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def getCollectionListInfo(keyword: str,startDate: str,endDate: str,status: str,dataset: str):
    try:
        db = init_monogdb()
        query = {"metadata.source": "api"}
        if dataset:
            query["datasetId"] = ObjectId(dataset)
        if keyword:
            query["name"] = {"$regex": f".*{keyword}.*"}
        if startDate and endDate:
            query["$and"] = [{"createTime": {"$gte": datetime.fromisoformat(startDate).replace(tzinfo=timezone.utc)}},
                {"createTime": {"$lte": datetime.fromisoformat(endDate).replace(hour=23, minute=59, second=59, microsecond=999999, tzinfo=timezone.utc)}}]
        if status:
            query["metadata.audit"] = status
        collections = db['dataset_collections'].find(query)
        result = []
        for collection in collections:
            result.append({"id": str(collection["_id"]),
                           "datasetId": str(collection["datasetId"]),
                           "datasetname": global_datasets_dic[str(collection["datasetId"])],
                           "name": collection["name"],
                           "time": collection["createTime"],
                           "audit": collection["metadata"]["audit"],
                           "cpxh": collection["metadata"].get("cpxh",""),
                           "size": collection["metadata"]["size"]})
        return {"code": 200, "data": result, "serverIP": templatesIP}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def deleteQA(body: dict):
    try:
        id = body.get("id","")
        if not id:
            return {"code": 500, "data": [], "message": "参数错误"}
        response = requests.delete(f"http://{apiIP}:3000/api/core/dataset/data/delete?id="+id,headers=headers).json()
        if response["code"] == 200:
            return {"code": 200}
        else:
            raise HTTPException(status_code=response["code"], detail=str(response))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def auditCollection(body: dict):
    try:
        collectionId = body.get("collectionId","")
        token = body.get("token","")
        passwordmd5 = get_token(baseusername,basepassword)
        if not collectionId:
            return {"code": 500, "data": [], "message": "参数错误"}
        elif token != passwordmd5:
            return {"code": 500, "data": [], "message": "无权限审批"}
        else:
            db = init_monogdb()
            db['dataset_collections'].update_one({"_id": ObjectId(collectionId)},{"$set":{"metadata.audit":"1","forbid": False}})

            # 审批通过的文档写入知识清单
            collobj= db["dataset_collections"].find_one({"_id": ObjectId(collectionId)})
            datasetId = str(collobj["datasetId"])
            createTime = collobj["createTime"]
            db["files_list_wh"].insert_one({
                "datasetId": datasetId,
                "datasetname": global_datasets_dic[datasetId],
                "collectionId": collectionId,
                "name": global_datasets_collections_dic[collectionId],
                "creatTime": createTime,
                "metadata": {}
            })
            return {"code": 200, "data": []}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


def get_indexs(indexes,tag=0):
    result = []
    for index in indexes:
        if index.get("defaultIndex") == True or index.get("type") == "default":
            index["_id"] = str(index["_id"])
            result.append(index)
        else:
            if tag == 1:
                text = {"text":index["text"]}
            else:
                text = {"text":index["text"],"editing":False}
            result.append(text)
    return result

def get_image(q):
    pattern = r'!\[([^\]]*)\]\((/api/system/img/[^)]+\.(?:png|jpg|jpeg))\)'
    replacement = f'<image src="http://{apiIP}:3000'+r'\2" alt="\1" loading="lazy" referrerpolicy="no-referrer"/>'
    new_string = re.sub(pattern, replacement, q)
    return new_string

def del_image(q):
    # 定义正则表达式模式
    pattern = f'<image src="http://{apiIP}:3000'+r'(/api/system/img/[^"]+\.(?:png|jpg|jpeg))" alt="([^"]*)" loading="lazy" referrerpolicy="no-referrer"/>'
    # 还原为 ![](...)
    replacement = r'![\2](\1)'
    # 使用 re.sub 进行还原
    original_string = re.sub(pattern, replacement, q)
    return original_string


async def getDatasetdatas(collectionId: str):
    try:
        global global_datasets_collections_dic
        db = init_monogdb()
        try:
            audit = db['dataset_collections'].find_one({"_id": ObjectId(collectionId)})["metadata"]["audit"]
        except:
            audit = "1"
        dataids_list = list(db['dataset_datas'].find({"collectionId": ObjectId(collectionId)}).sort({"chunkIndex": 1 }))
        result = []
        for data in dataids_list:
            indexes = get_indexs(data["indexes"])
            data["q"] = get_image(data["q"])
            result.append([str(data["_id"]),data["q"],data["a"] or '',indexes])
        return {"code": 200, "data": {"name":global_datasets_collections_dic[collectionId],"q":result,"audit":audit}}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def updateDatasetdatas(body: dict):
    try:
        id = body["data"][0]
        q = body["data"][1]
        a = body["data"][2] or ''
        indexes = body["data"][3]
        if not id:
            return {"code": 500, "data": [], "message": "参数错误"}
        else:
            new_indexes = []
            for index in indexes:
                if not index.get("text"):
                    continue
                if "editing" in index:
                    del index["editing"]
                new_indexes.append(index)
            # 待去除增加图片参数
            q = del_image(q)
            data = {"dataId":id,"q":q,"a":a,"indexes":new_indexes}
            headers["Content-Type"] = "application/json"
            response = requests.post(f"http://{apiIP}:3000/api/core/dataset/data/update",data=json.dumps(data),headers=headers).json()
            if response["code"] == 200:
                return {"code": 200}
            else:
                raise HTTPException(status_code=response["code"], detail=str(response))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def deleteCollection(collectionId: str):
    try:
        response = requests.delete(f"http://{apiIP}:3000/api/core/dataset/collection/delete?id="+collectionId,headers=headers).json()
        if response["code"] == 200:
            return {"code": 200}
        else:
            raise HTTPException(status_code=response["code"], detail=str(response))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def index(request: Request):
    global global_datasets_dic
    global global_datasets_collections_dic
    if not global_datasets_dic or not global_datasets_collections_dic:
        get_global_data()
    with open(os.path.join("media", "index.html"), "r", encoding="utf-8") as file:
        html_content = file.read()
    return HTMLResponse(content=html_content, status_code=200)


async def login(body: dict):
    try:
        # 这里可以添加实际的登录逻辑，比如验证账号和密码
        if body.get("username") == baseusername and body.get("password") == basepassword:  # 示例：固定账号密码
            passwordmd5 = get_token(baseusername,basepassword)
            return {"code": 200, "message": "登录成功","token": passwordmd5}
        else:
            return {"code": 401, "message": "账号或密码错误"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


def get_filename_from_url(url):
    # 从 URL 中提取文件名
    parsed_url = urlparse(url)
    filename = os.path.basename(unquote(parsed_url.path))
    return filename

def get_filename_from_headers(headers):
    # 从 Content-Disposition 头中提取文件名
    content_disposition = headers.get("Content-Disposition", "")
    if "filename=" in content_disposition:
        filename = content_disposition.split("filename=")[1].strip('"\'')
        return filename
    return None

async def savebs(body: dict):
    try:
        urllist = body.get("urllist",[])
        collectionlist = []
        file_content = b""
        filename = ""
        content_type = ""
        doctag = False
        for url in urllist:
            fileurl = "https://zktecoaihub.com"+url
            response = requests.get(fileurl)

            # 获取文件名
            filename_from_url = get_filename_from_url(fileurl)
            # filename_from_headers = get_filename_from_headers(response.headers)
            file_content += response.content
            if not doctag:
                if "doc" in filename_from_url:
                    doctag = True
                filename = filename_from_url
                content_type = response.headers.get("Content-Type", "application/octet-stream")

        data = {
            "datasetId": "6777ca0d9f5a5d92bd1c6fac",  # 知识库ID
            "parentId": "",  # 父级ID
            "trainingType": "chunk",  # 训练模式
            "chunkSize": 1800,  # chunk长度
            "chunkSplitter": "",  # 自定义分割符号
            "qaPrompt": ""  # qa拆分提示词
        }
        data_json = json.dumps(data)
        files = {"file": (quote(filename), file_content, content_type),"data": (None, data_json, "application/json")}
        response = requests.post(f"http://{apiIP}:3000/api/core/dataset/collection/create/localFile",headers=headers,files=files).json()
        if response["code"] == 200:
            collectionlist.append(response["data"]["collectionId"])
            collectionId = ObjectId(response["data"]["collectionId"])
            db = init_monogdb()
            collection = list(db['dataset_collections'].find({"_id": collectionId}))[0]
            metadata = collection["metadata"]
            metadata["source"] = "bs"
            metadata["isindex"] = "0"
            db['dataset_collections'].update_one({"_id":collectionId},{"$set":{"metadata":metadata}})
        else:
            raise HTTPException(status_code=response["code"], detail=str(response))
        return {"code": 200, "data": collectionlist}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))