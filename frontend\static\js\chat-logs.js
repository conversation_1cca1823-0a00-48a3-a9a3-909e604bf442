/**
 * 对话日志管理 - JavaScript
 */

// 对话日志应用组件
const ChatLogsApp = {
  init() {
    new Vue({
      el: '#app',
      data() {
        return {
          // 基础数据
          appList: [],
          selectedApp: '',
          dateRange: [],
          loading: false,
          exporting: false,
          
          // 表格数据
          tableData: [],
          currentPage: 1,
          pageSize: 10,
          total: 0,
          
          // 详情对话框
          detailDialogVisible: false,
          currentDetail: {},
          
          // 反馈对话框
          feedbackDialogVisible: false,
          feedbackForm: {
            chatId: '',
            type: '',
            content: ''
          },
          submitting: false,
          
          // 侧边栏状态
          activeMenu: '1',
          sfilesh: false
        };
      },
      
      mounted() {
        this.initializeApp();
      },
      
      methods: {
        /**
         * 初始化应用
         */
        async initializeApp() {
          // 设置默认日期范围（最近3天）
          const today = new Date();
          const threeDaysAgo = new Date();
          threeDaysAgo.setDate(today.getDate() - 3);
          this.dateRange = [threeDaysAgo, today];
          
          // 加载应用列表
          await this.loadAppList();
          
          // 如果有应用，自动查询第一个应用的日志
          if (this.appList.length > 0) {
            this.selectedApp = this.appList[0].id;
            this.queryLogs();
          }
        },
        
        /**
         * 加载应用列表
         */
        async loadAppList() {
          try {
            const response = await this.$api.get('/getDatasList/');
            if (response.code === 200) {
              this.appList = response.data;
            }
          } catch (error) {
            console.error('加载应用列表失败:', error);
            this.$showError('加载应用列表失败');
          }
        },
        
        /**
         * 查询日志
         */
        async queryLogs() {
          if (!this.selectedApp) {
            this.$showWarning('请先选择应用');
            return;
          }
          
          if (!this.dateRange || this.dateRange.length !== 2) {
            this.$showWarning('请选择日期范围');
            return;
          }
          
          this.loading = true;
          try {
            const startDate = dayjs(this.dateRange[0]).format('YYYY-MM-DDTHH:mm:ss.000z');
            const endDate = dayjs(this.dateRange[1]).format('YYYY-MM-DDTHH:mm:ss.000z');
            
            const response = await this.$api.get('/getChatLogs/', {
              dateStart: startDate,
              dateEnd: endDate,
              pageNum: this.currentPage,
              appId: this.selectedApp
            });
            
            if (response.code === 200) {
              this.total = response.data.total;
              // 获取详细数据
              await this.loadDetailedData(startDate, endDate);
            }
          } catch (error) {
            console.error('查询日志失败:', error);
            this.$showError('查询日志失败');
          } finally {
            this.loading = false;
          }
        },
        
        /**
         * 加载详细数据
         */
        async loadDetailedData(startDate, endDate) {
          try {
            const response = await this.$api.post('/getPaginationRecords/', {
              dateStart: startDate,
              dateEnd: endDate,
              appId: this.selectedApp,
              pageNum: this.currentPage,
              pageSize: this.pageSize
            });
            
            if (response.code === 200) {
              this.tableData = response.data.data.map(item => ({
                id: item.id,
                time: item.time,
                userChatInput: item.userChatInput || '',
                assistantChatOutput: item.assistantChatOutput || '',
                ywk: item.ywk || '',
                appId: this.selectedApp
              }));
            }
          } catch (error) {
            console.error('加载详细数据失败:', error);
            this.$showError('加载详细数据失败');
          }
        },
        
        /**
         * 分页变化
         */
        handleCurrentChange(page) {
          this.currentPage = page;
          this.queryLogs();
        },
        
        /**
         * 查看详情
         */
        viewDetail(row) {
          this.currentDetail = { ...row };
          this.detailDialogVisible = true;
        },
        
        /**
         * 保存反馈
         */
        saveFeedback(row) {
          this.feedbackForm = {
            chatId: row.id,
            type: '',
            content: ''
          };
          this.feedbackDialogVisible = true;
        },
        
        /**
         * 提交反馈
         */
        async submitFeedback() {
          if (!this.feedbackForm.type) {
            this.$showWarning('请选择反馈类型');
            return;
          }
          
          this.submitting = true;
          try {
            const response = await this.$api.post('/saveFeedbacks/', {
              chatId: this.feedbackForm.chatId,
              type: this.feedbackForm.type,
              content: this.feedbackForm.content
            });
            
            if (response.code === 200) {
              this.$showSuccess('反馈提交成功');
              this.feedbackDialogVisible = false;
            } else {
              this.$showError('反馈提交失败');
            }
          } catch (error) {
            console.error('提交反馈失败:', error);
            this.$showError('提交反馈失败');
          } finally {
            this.submitting = false;
          }
        },
        
        /**
         * 导出数据
         */
        async exportData() {
          if (!this.selectedApp || !this.dateRange) {
            this.$showWarning('请先选择应用和日期范围');
            return;
          }
          
          this.exporting = true;
          try {
            // 这里可以实现导出功能
            this.$showSuccess('导出功能开发中...');
          } catch (error) {
            console.error('导出失败:', error);
            this.$showError('导出失败');
          } finally {
            this.exporting = false;
          }
        },
        
        /**
         * 格式化时间
         */
        formatTime(row, column, cellValue) {
          return dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss');
        },
        
        /**
         * 获取应用名称
         */
        getAppName(appId) {
          const app = this.appList.find(a => a.id === appId);
          return app ? app.name : '未知应用';
        },
        
        /**
         * 处理菜单选择
         */
        handleMenuSelect(index) {
          if (index === '2') {
            window.router.navigate('knowledge-base');
          } else if (index === '1') {
            this.activeMenu = index;
          } else if (index === '3') {
            window.router.navigate('image-management');
          } else if (index === '4') {
            window.router.navigate('audit');
          }
        }
      }
    });
  }
};

// 导出到全局
window.ChatLogsApp = ChatLogsApp;
