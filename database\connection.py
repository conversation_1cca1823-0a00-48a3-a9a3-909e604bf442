"""
数据库连接管理
提供MongoDB连接和基础操作
"""

from pymongo import MongoClient
from pymongo.database import Database
from typing import Optional
from ..config.settings import settings


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self._client: Optional[MongoClient] = None
        self._database: Optional[Database] = None
    
    def get_client(self) -> MongoClient:
        """获取MongoDB客户端"""
        if self._client is None:
            self._client = MongoClient(settings.mongodb_url)
        return self._client
    
    def get_database(self) -> Database:
        """获取数据库实例"""
        if self._database is None:
            client = self.get_client()
            self._database = client[settings.MONGODB_DATABASE]
        return self._database
    
    def close_connection(self):
        """关闭数据库连接"""
        if self._client:
            self._client.close()
            self._client = None
            self._database = None


# 全局数据库管理器实例
db_manager = DatabaseManager()


def get_database() -> Database:
    """获取数据库实例的便捷函数"""
    return db_manager.get_database()


def init_mongodb() -> Database:
    """初始化MongoDB连接（兼容原有代码）"""
    return get_database()
