"""
数据验证工具
提供各种数据验证功能
"""

import re
from typing import Optional
from bson.objectid import ObjectId


def is_valid_object_id(obj_id: str) -> bool:
    """验证ObjectId是否有效"""
    try:
        ObjectId(obj_id)
        return True
    except:
        return False


def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def validate_phone(phone: str) -> bool:
    """验证手机号格式"""
    pattern = r'^1[3-9]\d{9}$'
    return re.match(pattern, phone) is not None


def sanitize_filename(filename: str) -> str:
    """清理文件名，移除非法字符"""
    # 移除或替换非法字符
    illegal_chars = r'[<>:"/\\|?*]'
    sanitized = re.sub(illegal_chars, '_', filename)
    
    # 移除开头和结尾的空格和点
    sanitized = sanitized.strip(' .')
    
    # 确保文件名不为空
    if not sanitized:
        sanitized = "unnamed_file"
    
    return sanitized


def validate_file_extension(filename: str, allowed_extensions: list) -> bool:
    """验证文件扩展名"""
    if not filename:
        return False
    
    extension = filename.lower().split('.')[-1]
    return extension in [ext.lower() for ext in allowed_extensions]


def validate_file_size(size: int, max_size_mb: int = 100) -> bool:
    """验证文件大小"""
    max_size_bytes = max_size_mb * 1024 * 1024
    return 0 < size <= max_size_bytes
