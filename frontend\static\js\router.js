/**
 * 前端路由系统
 * 实现单页面应用的路由管理
 */

class FrontendRouter {
  constructor() {
    this.routes = new Map();
    this.currentRoute = '';
    this.defaultRoute = 'knowledge-base';
    this.init();
  }

  /**
   * 初始化路由系统
   */
  init() {
    // 注册默认路由
    this.registerRoutes();
    
    // 监听浏览器前进后退
    window.addEventListener('popstate', (event) => {
      this.handleRouteChange();
    });
    
    // 初始路由
    this.handleRouteChange();
  }

  /**
   * 注册路由
   */
  registerRoutes() {
    // 知识库管理页面（文档库）
    this.register('knowledge-base', {
      template: '/frontend/templates/index.html',
      title: '知识库管理 - 文档库',
      component: 'KnowledgeBaseApp',
      scripts: [
        '/frontend/static/js/modules/data-manager.js',
        '/frontend/static/js/modules/file-manager.js',
        '/frontend/static/js/modules/auth-manager.js',
        '/frontend/static/js/knowledge-base.js'
      ],
      styles: ['/frontend/static/css/knowledge-base.css']
    });

    // 对话日志页面
    this.register('chat-logs', {
      template: '/frontend/templates/chat-logs.html',
      title: '知识库管理 - 对话日志',
      component: 'ChatLogsApp',
      scripts: ['/frontend/static/js/chat-logs.js'],
      styles: ['/frontend/static/css/chat-logs.css']
    });

    // 图片管理页面（知识库片段修改）
    this.register('image-management', {
      template: '/frontend/templates/image-management.html',
      title: '知识库管理 - 知识库片段修改',
      component: 'ImageManagementApp',
      scripts: ['/frontend/static/js/image-management.js'],
      styles: ['/frontend/static/css/image-management.css']
    });

    // 审核页面（知识库片段审核）
    this.register('audit', {
      template: '/frontend/templates/audit.html',
      title: '知识库管理 - 知识库片段审核',
      component: 'AuditApp',
      scripts: ['/frontend/static/js/audit.js'],
      styles: ['/frontend/static/css/audit.css']
    });
  }

  /**
   * 注册单个路由
   */
  register(path, config) {
    this.routes.set(path, config);
  }

  /**
   * 导航到指定路由
   */
  navigate(path, pushState = true) {
    if (!this.routes.has(path)) {
      console.warn(`路由 ${path} 不存在`);
      path = this.defaultRoute;
    }

    if (pushState && path !== this.currentRoute) {
      history.pushState({ path }, '', `#${path}`);
    }

    this.loadRoute(path);
  }

  /**
   * 处理路由变化
   */
  handleRouteChange() {
    const hash = window.location.hash.slice(1);
    const path = hash || this.defaultRoute;
    this.navigate(path, false);
  }

  /**
   * 加载路由
   */
  async loadRoute(path) {
    const route = this.routes.get(path);
    if (!route) return;

    try {
      // 显示加载状态
      this.showLoading();

      // 更新页面标题
      document.title = route.title;

      // 加载样式文件
      await this.loadStyles(route.styles || []);

      // 加载模板
      await this.loadTemplate(route.template);

      // 加载脚本文件
      await this.loadScripts(route.scripts || []);

      // 初始化组件
      if (route.component && window[route.component]) {
        window[route.component].init();
      }

      this.currentRoute = path;
      this.hideLoading();

    } catch (error) {
      console.error('路由加载失败:', error);
      this.showError('页面加载失败');
    }
  }

  /**
   * 加载模板
   */
  async loadTemplate(templateUrl) {
    try {
      const response = await fetch(templateUrl);
      const html = await response.text();
      
      // 更新页面内容
      const appContainer = document.getElementById('app');
      if (appContainer) {
        appContainer.innerHTML = html;
      }
    } catch (error) {
      console.error('模板加载失败:', error);
      throw error;
    }
  }

  /**
   * 加载样式文件
   */
  async loadStyles(styleUrls) {
    const promises = styleUrls.map(url => {
      return new Promise((resolve, reject) => {
        // 检查是否已经加载
        if (document.querySelector(`link[href="${url}"]`)) {
          resolve();
          return;
        }

        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = url;
        link.onload = resolve;
        link.onerror = reject;
        document.head.appendChild(link);
      });
    });

    await Promise.all(promises);
  }

  /**
   * 加载脚本文件
   */
  async loadScripts(scriptUrls) {
    for (const url of scriptUrls) {
      await this.loadScript(url);
    }
  }

  /**
   * 加载单个脚本文件
   */
  loadScript(url) {
    return new Promise((resolve, reject) => {
      // 检查是否已经加载
      if (document.querySelector(`script[src="${url}"]`)) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = url;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  /**
   * 显示加载状态
   */
  showLoading() {
    const loadingEl = document.getElementById('loading');
    if (loadingEl) {
      loadingEl.style.display = 'flex';
    } else {
      // 创建加载元素
      const loading = document.createElement('div');
      loading.id = 'loading';
      loading.innerHTML = `
        <div style="
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(255, 255, 255, 0.8);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 9999;
        ">
          <div style="text-align: center;">
            <i class="el-icon-loading" style="font-size: 32px; color: #409EFF;"></i>
            <p style="margin-top: 10px; color: #666;">页面加载中...</p>
          </div>
        </div>
      `;
      document.body.appendChild(loading);
    }
  }

  /**
   * 隐藏加载状态
   */
  hideLoading() {
    const loadingEl = document.getElementById('loading');
    if (loadingEl) {
      loadingEl.style.display = 'none';
    }
  }

  /**
   * 显示错误信息
   */
  showError(message) {
    this.hideLoading();
    if (window.ELEMENT && window.ELEMENT.Message) {
      window.ELEMENT.Message.error(message);
    } else {
      alert(message);
    }
  }

  /**
   * 获取当前路由
   */
  getCurrentRoute() {
    return this.currentRoute;
  }

  /**
   * 获取所有路由
   */
  getRoutes() {
    return Array.from(this.routes.keys());
  }
}

// 创建全局路由实例
window.router = new FrontendRouter();

// 导出路由类
window.FrontendRouter = FrontendRouter;
